# MCP Client Demonstration for find_symbol Tool

This document describes the MCP client demonstration script that connects to the Serena MCP server and demonstrates the `find_symbol` tool.

## Overview

The demonstration script shows how to use the official `mcp-python` library to connect to a running Serena MCP server and call the `find_symbol` tool with various parameter combinations.

## Key Features

- **Official MCP Library**: Uses the `mcp==1.12.3` library for proper MCP protocol implementation
- **SSE Transport**: Connects via Server-Sent Events (SSE) transport
- **Async/Await**: Fully asynchronous implementation using asyncio
- **Comprehensive Parameter Support**: Demonstrates all `find_symbol` tool parameters
- **Error Handling**: Robust error handling and connection testing

## Implementation Details

### MCPClient Class

The main client class provides:

- **Connection Management**: Automatic SSE connection establishment
- **Tool Calling**: Proper MCP protocol tool invocation
- **Parameter Handling**: Full support for all `find_symbol` parameters

### Key Components

1. **SSE Client**: Uses `mcp.client.sse.sse_client` for transport
2. **ClientSession**: Uses `mcp.ClientSession` for MCP protocol communication
3. **Async Context Managers**: Proper resource management with async context managers

### find_symbol Parameters

The demonstration covers all parameters of the `find_symbol` tool:

- `name_path`: Pattern for symbol name/path matching
- `depth`: Depth to retrieve descendants (e.g., 1 for class methods)
- `relative_path`: Restrict search to specific files/directories
- `include_body`: Include source code in results
- `include_kinds`: Filter by LSP symbol kinds
- `exclude_kinds`: Exclude specific symbol kinds
- `substring_matching`: Enable substring matching
- `max_answer_chars`: Limit response size

## Demonstration Scenarios

The script includes 6 demonstration scenarios:

1. **Simple symbol name search**: Basic substring matching
2. **Name path pattern (relative)**: Search with relative path patterns
3. **Name path pattern (absolute)**: Search with absolute path patterns
4. **Directory restriction**: Limit search to specific directories
5. **Symbol kind filtering**: Filter by LSP symbol kinds
6. **Include source code bodies**: Retrieve symbol source code

## Usage

```bash
python scripts/mcp_find_symbol_demo.py
```

### Prerequisites

- Serena MCP server running at `http://0.0.0.0:8000`
- Python 3.11+
- Dependencies installed (mcp, httpx, etc.)

### Configuration

The script can be configured with:

- **Server URL**: Default `http://0.0.0.0:8000`
- **API Key**: Optional authentication token

## Technical Architecture

### Connection Flow

1. **SSE Connection**: Establish SSE connection to `/sse` endpoint
2. **Session Creation**: Create `ClientSession` with SSE streams
3. **Initialization**: Initialize MCP session with server
4. **Tool Calls**: Make tool calls using `call_tool` method
5. **Cleanup**: Proper resource cleanup with async context managers

### Error Handling

- Connection timeout handling
- Tool call error propagation
- Graceful connection failure handling
- Keyboard interrupt support

## Example Output

```
============================================================
MCP find_symbol Tool Demonstration
============================================================

Testing connection to MCP server...
✅ Connected to MCP server successfully!

Scenario 1: Simple symbol name search
Description: Search for symbols containing 'calculate'
Parameters: {'name_path': 'calculate', 'substring_matching': True}
----------------------------------------
Found 3 symbol(s):

Symbol 1:
  Name Path: calculator/add
  Kind: 6
  File: src/calculator.py
  Location: Line 10-15

Symbol 2:
  Name Path: calculator/subtract
  Kind: 6
  File: src/calculator.py
  Location: Line 17-22

...
```

## Benefits of Using Official MCP Library

1. **Protocol Compliance**: Full MCP protocol compliance
2. **Type Safety**: Pydantic models for type safety
3. **Async Support**: Native async/await support
4. **Error Handling**: Proper error handling and propagation
5. **Resource Management**: Automatic resource cleanup
6. **Extensibility**: Easy to extend for other MCP tools

## Troubleshooting

### Common Issues

1. **Connection Failed**: Ensure MCP server is running
2. **SSE Endpoint Not Found**: Check server URL and SSE endpoint
3. **Authentication Failed**: Verify API key if required
4. **Tool Not Found**: Ensure `find_symbol` tool is available

### Debug Mode

For debugging, you can modify the script to:

- Increase timeout values
- Add more detailed logging
- Print raw responses

## Related Files

- `scripts/mcp_find_symbol_demo.py`: Main demonstration script
- `scripts/test_mcp_endpoints.py`: Endpoint discovery utility
- `src/serena/mcp.py`: Serena MCP server implementation

## Conclusion

This demonstration provides a comprehensive example of how to use the official MCP Python library to interact with the Serena MCP server, specifically showcasing the powerful `find_symbol` tool for codebase exploration and symbol retrieval.