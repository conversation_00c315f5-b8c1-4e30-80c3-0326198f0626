import subprocess
import json
import threading
import time
import sys
import os
from pathlib import Path

class JDTLSStdioClient:
    def __init__(self, jdtls_command, log_file=None):
        self.process = None
        self.jdtls_command = jdtls_command
        self.log_file = log_file
        self.stderr_buffer = []
        self.stdout_buffer = []
        self.is_running = False
        
    def start(self):
        """启动JDTLS进程"""
        try:
            # 如果指定了日志文件，将stderr重定向到文件
            stderr_dest = subprocess.PIPE
            if self.log_file:
                stderr_dest = open(self.log_file, 'w', encoding='utf-8')
                print(f"JDTLS日志将保存到: {self.log_file}")
            
            self.process = subprocess.Popen(
                self.jdtls_command,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=stderr_dest,
                text=True,
                bufsize=0,  # 无缓冲
                encoding='utf-8'
            )
            
            self.is_running = True
            print(f"JDTLS进程已启动 (PID: {self.process.pid})")
            
            # 启动日志监听线程
            self._start_log_listeners()
            
            return True
            
        except Exception as e:
            print(f"启动JDTLS进程失败: {e}")
            return False
    
    def _start_log_listeners(self):
        """启动日志监听线程"""
        # 监听stdout（LSP消息）
        self.stdout_thread = threading.Thread(
            target=self._listen_stdout, 
            daemon=True,
            name="StdoutListener"
        )
        self.stdout_thread.start()
        
        # 如果stderr是管道，也监听它
        if self.process.stderr and hasattr(self.process.stderr, 'read'):
            self.stderr_thread = threading.Thread(
                target=self._listen_stderr,
                daemon=True,
                name="StderrListener"
            )
            self.stderr_thread.start()
    
    def _listen_stdout(self):
        """监听stdout（LSP协议消息）"""
        print("开始监听JDTLS stdout...")
        while self.is_running and self.process and self.process.stdout:
            try:
                line = self.process.stdout.readline()
                if not line:
                    break
                
                # 保存到缓冲区
                self.stdout_buffer.append(('stdout', line.strip()))
                
                # 实时打印（可选）
                if len(line.strip()) > 0:
                    print(f"[JDTLS-STDOUT] {line.strip()}")
                    
            except Exception as e:
                print(f"读取stdout时出错: {e}")
                break
    
    def _listen_stderr(self):
        """监听stderr（日志和错误信息）"""
        print("开始监听JDTLS stderr...")
        while self.is_running and self.process and self.process.stderr:
            try:
                line = self.process.stderr.readline()
                if not line:
                    break
                
                # 保存到缓冲区
                self.stderr_buffer.append(('stderr', line.strip()))
                
                # 实时打印到控制台
                if len(line.strip()) > 0:
                    print(f"[JDTLS-STDERR] {line.strip()}")
                    
            except Exception as e:
                print(f"读取stderr时出错: {e}")
                break
    
    def send_request(self, json_request):
        """发送LSP请求"""
        if not self.process or not self.is_running:
            print("错误: JDTLS进程未运行")
            return False
            
        try:
            content = json.dumps(json_request)
            header = f"Content-Length: {len(content)}\r\n\r\n"
            message = header + content
            self.process.stdin.write(message)
            self.process.stdin.flush()
            print(f"已发送请求: {json.dumps(json_request, indent=2)}")
            return True
        except Exception as e:
            print(f"发送请求时出错: {e}")
            return False
    
    def receive_response(self, timeout=10):
        """接收LSP响应"""
        start_time = time.time()
        buffer = ""
        content_length = -1
        
        while time.time() - start_time < timeout:
            if not self.is_running:
                break
                
            # 从stdout缓冲区查找完整的消息
            for i, (source, line) in enumerate(self.stdout_buffer):
                if source == 'stdout':
                    buffer += line + '\n'
                    
                    # 检查是否找到Content-Length头
                    if content_length == -1 and 'Content-Length:' in line:
                        try:
                            content_length = int(line.split(':')[1].strip())
                        except ValueError:
                            continue
                    
                    # 检查是否到达消息头结束
                    if line == '' and content_length > 0:
                        # 查找后续的content_length个字符
                        remaining_content = ""
                        chars_needed = content_length
                        
                        # 继续从缓冲区读取剩余内容
                        for j in range(i + 1, len(self.stdout_buffer)):
                            if self.stdout_buffer[j][0] == 'stdout':
                                line_content = self.stdout_buffer[j][1]
                                if len(line_content) <= chars_needed:
                                    remaining_content += line_content
                                    chars_needed -= len(line_content)
                                else:
                                    remaining_content += line_content[:chars_needed]
                                    chars_needed = 0
                                    break
                                
                                if chars_needed == 0:
                                    break
                        
                        if chars_needed == 0:
                            # 清理已处理的消息
                            self.stdout_buffer = self.stdout_buffer[j+1:]
                            try:
                                return json.loads(remaining_content)
                            except json.JSONDecodeError as e:
                                print(f"解析JSON响应时出错: {e}")
                                return None
            
            time.sleep(0.1)  # 短暂休眠避免CPU过度使用
        
        print(f"接收响应超时 ({timeout}秒)")
        return None
    
    def get_logs(self, max_lines=50):
        """获取最近的日志"""
        print(f"\n=== 最近 {max_lines} 条日志 ===")
        all_logs = self.stderr_buffer + self.stdout_buffer
        all_logs.sort(key=lambda x: x[0])  # 按时间顺序排序
        
        for source, line in all_logs[-max_lines:]:
            print(f"[{source.upper()}] {line}")
        print("=== 日志结束 ===\n")
    
    def save_logs_to_file(self, filename=None):
        """保存日志到文件"""
        if not filename:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"jdtls_logs_{timestamp}.txt"
        
        all_logs = self.stderr_buffer + self.stdout_buffer
        all_logs.sort(key=lambda x: x[0])
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"JDTLS日志 - {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("=" * 50 + "\n")
            for source, line in all_logs:
                f.write(f"[{source}] {line}\n")
        
        print(f"日志已保存到: {filename}")
        return filename
    
    def shutdown(self, timeout=5):
        """关闭JDTLS进程"""
        if not self.process:
            return
            
        self.is_running = False
        
        try:
            # 发送退出通知
            exit_notification = {
                "jsonrpc": "2.0",
                "method": "exit",
                "params": {}
            }
            self.send_request(exit_notification)
            
            # 等待进程正常退出
            for i in range(timeout * 2):
                if self.process.poll() is not None:
                    break
                time.sleep(0.5)
            
            # 如果进程还在运行，强制终止
            if self.process.poll() is None:
                print("强制终止JDTLS进程...")
                self.process.terminate()
                self.process.wait(timeout=2)
                
        except Exception as e:
            print(f"关闭进程时出错: {e}")
        
        finally:
            # 保存最终日志
            self.save_logs_to_file()
            print("JDTLS客户端已关闭")

def create_jdtls_command(workspace_dir, java_home=None, jdtls_home=None):
    """创建JDTLS启动命令"""
    # if java_home is None:
    java_home = "/home/<USER>/.serena/language_servers/static/EclipseJDTLS/vscode-java/extension/jre/21.0.7-linux-x86_64"
    
    # if jdtls_home is None:
    jdtls_home = '/home/<USER>/Downloads/jdt/'  # 请修改为你的JDTLS路径
    
    java_executable = str(Path(java_home) / 'bin' / 'java')
    jdtls_jar = str(Path(jdtls_home) / 'plugins' / 'org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar')
    
    command = [
        java_executable,
        '-Declipse.application=org.eclipse.jdt.ls.core.id1',
        '-Dosgi.bundles.defaultStartLevel=4',
        '-Declipse.product=org.eclipse.jdt.ls.core.product',
        '-Dlog.protocol=true',
        '-Dlog.level=ALL',
        '-noverify',
        '-Xmx1G',
        '--add-modules=ALL-SYSTEM',
        '--add-opens', 'java.base/java.util=ALL-UNNAMED',
        '--add-opens', 'java.base/java.lang=ALL-UNNAMED',
        '-jar', jdtls_jar,
        '-configuration', str(Path(jdtls_home) / 'config_linux'),
        '-data', workspace_dir,
        '-verbose'
    ]
    
    return command

def main():
    # 配置参数
    workspace_dir = "/home/<USER>/Codebase/gerrit/PATCHER"  # 请修改为你的工作目录
    log_file = "jdtls_stderr.log"  # stderr日志文件
    
    # 创建JDTLS命令
    jdtls_command = create_jdtls_command(workspace_dir)
    print(f"启动命令: {' '.join(jdtls_command)}")
    
    # 创建客户端
    client = JDTLSStdioClient(jdtls_command, log_file=None)
    
    try:
        # 启动JDTLS
        if not client.start():
            sys.exit(1)
        
        # 等待进程初始化
        print("等待JDTLS进程初始化...")
        time.sleep(3)
        
        # 显示初始日志
        client.get_logs(20)
        
        # 初始化请求
        initialize_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "processId": os.getpid(),
                "clientInfo": {
                    "name": "Python JDTLS Client",
                    "version": "1.0.0"
                },
                "rootUri": f"file://{workspace_dir}",
                "capabilities": {
                    "workspace": {
                        "configuration": True,
                        "workspaceFolders": True
                    },
                    "textDocument": {
                        "completion": {
                            "completionItem": {
                                "snippetSupport": True
                            }
                        }
                    }
                },
                "trace": "verbose"
            }
        }
        
        if client.send_request(initialize_request):
            response = client.receive_response(timeout=10)
            if response:
                print(f"初始化响应: {json.dumps(response, indent=2)}")
            else:
                print("未收到初始化响应")
        
        # 发送initialized通知
        initialized_notification = {
            "jsonrpc": "2.0",
            "method": "initialized",
            "params": {}
        }
        client.send_request(initialized_notification)
        
        # 等待服务器准备就绪
        print("等待服务器准备就绪...")
        time.sleep(5)
        
        # 显示当前日志
        # print("\n=== 服务器启动后的日志 ===")
        # client.get_logs(30)
        
        # # 示例：发送代码补全请求
        # test_file = f"file://{workspace_dir}/src/Main.java"
        # completion_request = {
        #     "jsonrpc": "2.0",
        #     "id": 2,
        #     "method": "textDocument/completion",
        #     "params": {
        #         "textDocument": {
        #             "uri": test_file
        #         },
        #         "position": {
        #             "line": 5,
        #             "character": 10
        #         }
        #     }
        # }
        
        # print(f"发送补全请求到: {test_file}")
        # if client.send_request(completion_request):
        #     response = client.receive_response(timeout=10)
        #     if response:
        #         print(f"补全响应: {json.dumps(response, indent=2)}")
        
        # 保持运行一段时间以观察日志
        print("\n监控日志中...按Ctrl+C退出")
        try:
            while True:
                time.sleep(5)
                # 定期显示最新日志
                print("\n=== 最新日志 ===")
                # client.get_logs(10)
        except KeyboardInterrupt:
            print("\n用户中断")
        
    except Exception as e:
        print(f"运行过程中出错: {e}")
    finally:
        client.shutdown()

if __name__ == "__main__":
    main()