import subprocess
import json
import threading
import time
import sys
import os
from pathlib import Path

class JDTLSStdioClient:
    def __init__(self, jdtls_command, log_file=None):
        self.process = None
        self.jdtls_command = jdtls_command
        self.log_file = log_file
        self.stderr_buffer = []
        self.stdout_buffer = []
        self.is_running = False
        
    def start(self):
        """启动JDTLS进程"""
        try:
            # 如果指定了日志文件，将stderr重定向到文件
            stderr_dest = subprocess.PIPE
            if self.log_file:
                stderr_dest = open(self.log_file, 'w', encoding='utf-8')
                print(f"JDTLS日志将保存到: {self.log_file}")
            
            self.process = subprocess.Popen(
                self.jdtls_command,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=stderr_dest,
                text=True,
                bufsize=0,  # 无缓冲
                encoding='utf-8'
            )
            
            self.is_running = True
            print(f"JDTLS进程已启动 (PID: {self.process.pid})")
            
            # 启动日志监听线程
            self._start_log_listeners()
            
            return True
            
        except Exception as e:
            print(f"启动JDTLS进程失败: {e}")
            return False
    
    def _start_log_listeners(self):
        """启动日志监听线程"""
        # 监听stdout（LSP消息）
        self.stdout_thread = threading.Thread(
            target=self._listen_stdout, 
            daemon=True,
            name="StdoutListener"
        )
        self.stdout_thread.start()
        
        # 如果stderr是管道，也监听它
        if self.process.stderr and hasattr(self.process.stderr, 'read'):
            self.stderr_thread = threading.Thread(
                target=self._listen_stderr,
                daemon=True,
                name="StderrListener"
            )
            self.stderr_thread.start()
    
    def _listen_stdout(self):
        """监听stdout（LSP协议消息）"""
        print("开始监听JDTLS stdout...")
        while self.is_running and self.process and self.process.stdout:
            try:
                line = self.process.stdout.readline()
                if not line:
                    break
                
                # 保存到缓冲区
                self.stdout_buffer.append(('stdout', line.strip()))
                
                # 实时打印（可选）
                if len(line.strip()) > 0:
                    print(f"[JDTLS-STDOUT] {line.strip()}")
                    
            except Exception as e:
                print(f"读取stdout时出错: {e}")
                break
    
    def _listen_stderr(self):
        """监听stderr（日志和错误信息）"""
        print("开始监听JDTLS stderr...")
        while self.is_running and self.process and self.process.stderr:
            try:
                line = self.process.stderr.readline()
                if not line:
                    break
                
                # 保存到缓冲区
                self.stderr_buffer.append(('stderr', line.strip()))
                
                # 实时打印到控制台
                if len(line.strip()) > 0:
                    print(f"[JDTLS-STDERR] {line.strip()}")
                    
            except Exception as e:
                print(f"读取stderr时出错: {e}")
                break
    
    def send_request(self, json_request):
        """发送LSP请求"""
        if not self.process or not self.is_running:
            print("错误: JDTLS进程未运行")
            return False
            
        try:
            content = json.dumps(json_request)
            header = f"Content-Length: {len(content)}\r\n\r\n"
            message = header + content
            self.process.stdin.write(message)
            self.process.stdin.flush()
            print(f"已发送请求: {json.dumps(json_request, indent=2)}")
            return True
        except Exception as e:
            print(f"发送请求时出错: {e}")
            return False
    
    def receive_response(self, timeout=10, target_id=None):
        """接收LSP响应"""
        start_time = time.time()

        while time.time() - start_time < timeout:
            if not self.is_running:
                break

            # 从stdout缓冲区查找完整的消息
            buffer_copy = self.stdout_buffer.copy()

            for i, (source, line) in enumerate(buffer_copy):
                if source == 'stdout':
                    # 检查是否是JSON响应（包含id字段）
                    if line.startswith('{"jsonrpc":"2.0","id":'):
                        try:
                            # 尝试解析JSON
                            response = json.loads(line)

                            # 如果指定了target_id，检查是否匹配
                            if target_id is None or response.get('id') == target_id:
                                # 从缓冲区移除已处理的消息
                                if i < len(self.stdout_buffer):
                                    self.stdout_buffer = self.stdout_buffer[i+1:]
                                return response

                        except json.JSONDecodeError:
                            # 如果解析失败，继续查找
                            continue

            time.sleep(0.1)  # 短暂休眠避免CPU过度使用

        print(f"接收响应超时 ({timeout}秒)")
        return None
    
    def get_logs(self, max_lines=50):
        """获取最近的日志"""
        print(f"\n=== 最近 {max_lines} 条日志 ===")
        all_logs = self.stderr_buffer + self.stdout_buffer
        all_logs.sort(key=lambda x: x[0])  # 按时间顺序排序
        
        for source, line in all_logs[-max_lines:]:
            print(f"[{source.upper()}] {line}")
        print("=== 日志结束 ===\n")
    
    def save_logs_to_file(self, filename=None):
        """保存日志到文件"""
        if not filename:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"jdtls_logs_{timestamp}.txt"
        
        all_logs = self.stderr_buffer + self.stdout_buffer
        all_logs.sort(key=lambda x: x[0])
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"JDTLS日志 - {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("=" * 50 + "\n")
            for source, line in all_logs:
                f.write(f"[{source}] {line}\n")
        
        print(f"日志已保存到: {filename}")
        return filename
    
    def shutdown(self, timeout=5):
        """关闭JDTLS进程"""
        if not self.process:
            return
            
        self.is_running = False
        
        try:
            # 发送退出通知
            exit_notification = {
                "jsonrpc": "2.0",
                "method": "exit",
                "params": {}
            }
            self.send_request(exit_notification)
            
            # 等待进程正常退出
            for i in range(timeout * 2):
                if self.process.poll() is not None:
                    break
                time.sleep(0.5)
            
            # 如果进程还在运行，强制终止
            if self.process.poll() is None:
                print("强制终止JDTLS进程...")
                self.process.terminate()
                self.process.wait(timeout=2)
                
        except Exception as e:
            print(f"关闭进程时出错: {e}")
        
        finally:
            # 保存最终日志
            self.save_logs_to_file()
            print("JDTLS客户端已关闭")

def find_java_files(workspace_dir):
    """查找workspace下的所有Java文件"""
    java_files = []
    for root, dirs, files in os.walk(workspace_dir):
        for file in files:
            if file.endswith('.java'):
                java_files.append(os.path.join(root, file))
    return java_files

def parse_and_display_symbols(symbols, indent=0):
    """解析并显示符号信息"""
    if not symbols:
        return

    for symbol in symbols:
        # 获取符号基本信息
        name = symbol.get('name', 'Unknown')
        kind = symbol.get('kind', 0)
        kind_name = get_symbol_kind_name(kind)

        # 获取位置信息
        range_info = symbol.get('range', {})
        start_line = range_info.get('start', {}).get('line', 0) + 1  # LSP使用0基索引
        start_char = range_info.get('start', {}).get('character', 0)
        end_line = range_info.get('end', {}).get('line', 0) + 1
        end_char = range_info.get('end', {}).get('character', 0)

        # 显示符号信息
        indent_str = "  " * indent
        print(f"{indent_str}[{kind_name}] {name} (行 {start_line}:{start_char} - {end_line}:{end_char})")

        # 如果有详细信息，也显示出来
        detail = symbol.get('detail', '')
        if detail:
            print(f"{indent_str}  详细: {detail}")

        # 递归显示子符号
        children = symbol.get('children', [])
        if children:
            parse_and_display_symbols(children, indent + 1)

def get_symbol_kind_name(kind):
    """将符号类型数字转换为可读名称"""
    symbol_kinds = {
        1: "File",
        2: "Module",
        3: "Namespace",
        4: "Package",
        5: "Class",
        6: "Method",
        7: "Property",
        8: "Field",
        9: "Constructor",
        10: "Enum",
        11: "Interface",
        12: "Function",
        13: "Variable",
        14: "Constant",
        15: "String",
        16: "Number",
        17: "Boolean",
        18: "Array",
        19: "Object",
        20: "Key",
        21: "Null",
        22: "EnumMember",
        23: "Struct",
        24: "Event",
        25: "Operator",
        26: "TypeParameter"
    }
    return symbol_kinds.get(kind, f"Unknown({kind})")

def create_jdtls_command(workspace_dir, java_home=None, jdtls_home=None):
    """创建JDTLS启动命令"""
    # if java_home is None:
    java_home = "/home/<USER>/.serena/language_servers/static/EclipseJDTLS/vscode-java/extension/jre/21.0.7-linux-x86_64"
    
    # if jdtls_home is None:
    jdtls_home = '/home/<USER>/Downloads/jdt/'  # 请修改为你的JDTLS路径
    
    java_executable = str(Path(java_home) / 'bin' / 'java')
    jdtls_jar = str(Path(jdtls_home) / 'plugins' / 'org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar')
    
    command = [
        java_executable,
        '-Declipse.application=org.eclipse.jdt.ls.core.id1',
        '-Dosgi.bundles.defaultStartLevel=4',
        '-Declipse.product=org.eclipse.jdt.ls.core.product',
        '-Dlog.protocol=true',
        '-Dlog.level=ALL',
        '-noverify',
        '-Xmx1G',
        '--add-modules=ALL-SYSTEM',
        '--add-opens', 'java.base/java.util=ALL-UNNAMED',
        '--add-opens', 'java.base/java.lang=ALL-UNNAMED',
        '-jar', jdtls_jar,
        '-configuration', str(Path(jdtls_home) / 'config_linux'),
        '-data', workspace_dir,
        '-verbose'
    ]
    
    return command

def main():
    # 配置参数 - 使用测试Java项目
    current_dir = os.path.dirname(os.path.abspath(__file__))
    workspace_dir = os.path.join(current_dir, "test/resources/repos/java/test_repo")

    # 如果测试目录不存在，使用用户指定的目录
    if not os.path.exists(workspace_dir):
        workspace_dir = "/home/<USER>/Codebase/gerrit/PATCHER"  # 请修改为你的工作目录
        print(f"测试目录不存在，使用: {workspace_dir}")
    else:
        print(f"使用测试Java项目: {workspace_dir}")

    log_file = "jdtls_stderr.log"  # stderr日志文件
    
    # 创建JDTLS命令
    jdtls_command = create_jdtls_command(workspace_dir)
    print(f"启动命令: {' '.join(jdtls_command)}")
    
    # 创建客户端
    client = JDTLSStdioClient(jdtls_command, log_file=None)
    
    try:
        # 启动JDTLS
        if not client.start():
            sys.exit(1)
        
        # 等待进程初始化
        print("等待JDTLS进程初始化...")
        time.sleep(3)
        
        # 显示初始日志
        client.get_logs(20)
        
        # 初始化请求
        initialize_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "processId": os.getpid(),
                "clientInfo": {
                    "name": "Python JDTLS Client",
                    "version": "1.0.0"
                },
                "rootUri": f"file://{workspace_dir}",
                "capabilities": {
                    "workspace": {
                        "configuration": True,
                        "workspaceFolders": True
                    },
                    "textDocument": {
                        "completion": {
                            "completionItem": {
                                "snippetSupport": True
                            }
                        }
                    }
                },
                "trace": "verbose"
            }
        }
        
        if client.send_request(initialize_request):
            response = client.receive_response(timeout=10, target_id=1)
            if response:
                print(f"初始化响应: {json.dumps(response, indent=2)}")
            else:
                print("未收到初始化响应")
        
        # 发送initialized通知
        initialized_notification = {
            "jsonrpc": "2.0",
            "method": "initialized",
            "params": {}
        }
        client.send_request(initialized_notification)
        
        # 等待服务器准备就绪
        print("等待服务器准备就绪...")
        time.sleep(5)

        # 查找workspace下的Java文件
        java_files = find_java_files(workspace_dir)
        if not java_files:
            print("未找到Java文件")
            return

        # 选择第一个Java文件进行测试
        test_java_file = java_files[0]
        print(f"选择测试文件: {test_java_file}")

        # 读取Java文件内容
        try:
            with open(test_java_file, 'r', encoding='utf-8') as f:
                file_content = f.read()
        except Exception as e:
            print(f"读取文件失败: {e}")
            return

        # 构造文件URI
        file_uri = f"file://{test_java_file}"

        # 1. 发送 didOpen 通知
        print(f"\n=== 发送 didOpen 通知 ===")
        did_open_notification = {
            "jsonrpc": "2.0",
            "method": "textDocument/didOpen",
            "params": {
                "textDocument": {
                    "uri": file_uri,
                    "languageId": "java",
                    "version": 1,
                    "text": file_content
                }
            }
        }

        if client.send_request(did_open_notification):
            print(f"已发送 didOpen 通知: {file_uri}")
            time.sleep(2)  # 等待服务器处理
        else:
            print("发送 didOpen 通知失败")
            return

        # 2. 发送 textDocument/documentSymbol 请求
        print(f"\n=== 发送 documentSymbol 请求 ===")
        document_symbol_request = {
            "jsonrpc": "2.0",
            "id": 2,
            "method": "textDocument/documentSymbol",
            "params": {
                "textDocument": {
                    "uri": file_uri
                }
            }
        }

        if client.send_request(document_symbol_request):
            print(f"已发送 documentSymbol 请求: {file_uri}")
            response = client.receive_response(timeout=15, target_id=2)
            if response:
                print(f"\n=== 文档符号响应 ===")
                print(json.dumps(response, indent=2, ensure_ascii=False))

                # 解析并显示符号信息
                if "result" in response and response["result"]:
                    print(f"\n=== 解析的符号信息 ===")
                    parse_and_display_symbols(response["result"])
                else:
                    print("未获取到符号信息")
            else:
                print("未收到 documentSymbol 响应")

        # 3. 发送 didClose 通知清理资源
        print(f"\n=== 发送 didClose 通知 ===")
        did_close_notification = {
            "jsonrpc": "2.0",
            "method": "textDocument/didClose",
            "params": {
                "textDocument": {
                    "uri": file_uri
                }
            }
        }
        client.send_request(did_close_notification)

        # 保持运行一段时间以观察日志
        print("\n监控日志中...按Ctrl+C退出")
        try:
            while True:
                time.sleep(5)
                # 定期显示最新日志
                print("\n=== 最新日志 ===")
                # client.get_logs(10)
        except KeyboardInterrupt:
            print("\n用户中断")
        
    except Exception as e:
        print(f"运行过程中出错: {e}")
    finally:
        client.shutdown()

if __name__ == "__main__":
    main()