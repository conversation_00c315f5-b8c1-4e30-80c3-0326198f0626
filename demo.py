import subprocess
import json
import threading
import time
import sys

class JDTLSStdioClient:
    def __init__(self, jdtls_command):
        self.process = subprocess.Popen(
            jdtls_command,
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=0,  # Unbuffered
            encoding='utf-8'
        )
        self.buffer = ""
        
    def send_request(self, json_request):
        content = json.dumps(json_request)
        header = f"Content-Length: {len(content)}\r\n\r\n"
        message = header + content
        self.process.stdin.write(message)
        self.process.stdin.flush()
        print(f"Sent request: {json_request}")
        
    def receive_response(self):
        # Read headers
        content_length = 0
        while True:
            line = self._read_line()
            if not line:
                continue
            if line.startswith('Content-Length:'):
                content_length = int(line.split(':')[1].strip())
            if line == '':
                break
        
        # Read content
        if content_length > 0:
            content = self.process.stdout.read(content_length)
            return json.loads(content)
        return None
        
    def _read_line(self):
        line = []
        while True:
            char = self.process.stdout.read(1)
            if char == '\r':
                # Skip the following '\n'
                self.process.stdout.read(1)
                break
            if not char:  # EOF
                return None
            line.append(char)
        return ''.join(line)
        
    def shutdown(self):
        # Send exit notification
        exit_notification = {
            "jsonrpc": "2.0",
            "method": "exit",
            "params": {}
        }
        self.send_request(exit_notification)
        
        # Wait for process to terminate
        try:
            self.process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            self.process.terminate()
            
    def listen_for_messages(self):
        """Listen for server-initiated messages"""
        def listen():
            while self.process.poll() is None:
                try:
                    response = self.receive_response()
                    if response:
                        print(f"Server message: {json.dumps(response, indent=2)}")
                except Exception as e:
                    print(f"Error reading response: {e}")
                    break
                    
        thread = threading.Thread(target=listen, daemon=True)
        thread.start()

def main():
    # Adjust the command to match your JDTLS installation
    jdtls_command = [
        "/home/<USER>/.serena/language_servers/static/EclipseJDTLS/vscode-java/extension/jre/21.0.7-linux-x86_64/bin/java", 
        "-Declipse.application=org.eclipse.jdt.ls.core.id1", 
        "-Dosgi.bundles.defaultStartLevel=4",
        "-Declipse.product=org.eclipse.jdt.ls.core.product",
        "-Dlog.level=ALL",
        "-Xmx1G",
        "--add-modules=ALL-SYSTEM",
        "--add-opens java.base/java.util=ALL-UNNAMED",
        "--add-opens java.base/java.lang=ALL-UNNAMED",
        "-jar /home/<USER>/Downloads/jdt/plugins/org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar",
        "-configuration /home/<USER>/Downloads/jdt/config_linux",
        "-data /home/<USER>/Codebase/gerrit/PATCHER",
    ]
    
    client = JDTLSStdioClient(jdtls_command)
    client.listen_for_messages()
    
    try:
        # Initialize request
        initialize_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "processId": 12345,
                "rootUri": "file:///path/to/your/java/project",
                "capabilities": {
                    "workspace": {
                        "configuration": True,
                        "workspaceFolders": True
                    },
                    "textDocument": {
                        "completion": {
                            "completionItem": {
                                "snippetSupport": True
                            }
                        }
                    }
                },
                "trace": "verbose"
            }
        }
        
        client.send_request(initialize_request)
        initialize_response = client.receive_response()
        print(f"Initialize response: {json.dumps(initialize_response, indent=2)}")
        
        # Initialized notification
        initialized_notification = {
            "jsonrpc": "2.0",
            "method": "initialized",
            "params": {}
        }
        client.send_request(initialized_notification)
        
        # Wait a bit for server to be ready
        time.sleep(2)
        
        # Example: Get completion
        completion_request = {
            "jsonrpc": "2.0",
            "id": 2,
            "method": "textDocument/completion",
            "params": {
                "textDocument": {
                    "uri": "file:///path/to/your/java/project/src/Main.java"
                },
                "position": {
                    "line": 5,
                    "character": 10
                }
            }
        }
        
        client.send_request(completion_request)
        completion_response = client.receive_response()
        print(f"Completion response: {json.dumps(completion_response, indent=2)}")
        
        # Keep alive
        time.sleep(5)
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        client.shutdown()

if __name__ == "__main__":
    main()