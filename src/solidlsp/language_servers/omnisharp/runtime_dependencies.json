{"_description": "Used to download the runtime dependencies for running OmniSharp. Obtained from https://github.com/dotnet/vscode-csharp/blob/main/package.json", "runtimeDependencies": [{"id": "OmniSharp", "description": "OmniSharp for Windows (.NET 4 / x86)", "url": "https://roslynomnisharp.blob.core.windows.net/releases/1.39.10/omnisharp-win-x86-1.39.10.zip", "installPath": ".omnisharp/1.39.10", "platforms": ["win32"], "architectures": ["x86"], "installTestPath": "./.omnisharp/1.39.10/OmniSharp.exe", "platformId": "win-x86", "isFramework": true, "integrity": "C81CE2099AD494EF63F9D88FAA70D55A68CF175810F944526FF94AAC7A5109F9", "dotnet_version": "4", "binaryName": "OmniSharp.exe"}, {"id": "OmniSharp", "description": "OmniSharp for Windows (.NET 6 / x86)", "url": "https://roslynomnisharp.blob.core.windows.net/releases/1.39.10/omnisharp-win-x86-net6.0-1.39.10.zip", "installPath": ".omnisharp/1.39.10-net6.0", "platforms": ["win32"], "architectures": ["x86"], "installTestPath": "./.omnisharp/1.39.10-net6.0/OmniSharp.dll", "platformId": "win-x86", "isFramework": false, "integrity": "B7E62415CFC3DAC2154AC636C5BF0FB4B2C9BBF11B5A1FBF72381DDDED59791E", "dotnet_version": "6", "binaryName": "OmniSharp.exe"}, {"id": "OmniSharp", "description": "OmniSharp for Windows (.NET 4 / x64)", "url": "https://roslynomnisharp.blob.core.windows.net/releases/1.39.10/omnisharp-win-x64-1.39.10.zip", "installPath": ".omnisharp/1.39.10", "platforms": ["win32"], "architectures": ["x86_64"], "installTestPath": "./.omnisharp/1.39.10/OmniSharp.exe", "platformId": "win-x64", "isFramework": true, "integrity": "BE0ED10AACEA17E14B78BD0D887DE5935D4ECA3712192A701F3F2100CA3C8B6E", "dotnet_version": "4", "binaryName": "OmniSharp.exe"}, {"id": "OmniSharp", "description": "OmniSharp for Windows (.NET 6 / x64)", "url": "https://roslynomnisharp.blob.core.windows.net/releases/1.39.10/omnisharp-win-x64-net6.0-1.39.10.zip", "installPath": ".omnisharp/1.39.10-net6.0", "platforms": ["win32"], "architectures": ["x86_64"], "installTestPath": "./.omnisharp/1.39.10-net6.0/OmniSharp.dll", "platformId": "win-x64", "isFramework": false, "integrity": "A73327395E7EF92C1D8E307055463DA412662C03F077ECC743462FD2760BB537", "dotnet_version": "6", "binaryName": "OmniSharp.exe"}, {"id": "OmniSharp", "description": "OmniSharp for Windows (.NET 4 / arm64)", "url": "https://roslynomnisharp.blob.core.windows.net/releases/1.39.10/omnisharp-win-arm64-1.39.10.zip", "installPath": ".omnisharp/1.39.10", "platforms": ["win32"], "architectures": ["arm64"], "installTestPath": "./.omnisharp/1.39.10/OmniSharp.exe", "platformId": "win-arm64", "isFramework": true, "integrity": "32FA0067B0639F87760CD1A769B16E6A53588C137C4D31661836CA4FB28D3DD6", "dotnet_version": "4", "binaryName": "OmniSharp.exe"}, {"id": "OmniSharp", "description": "OmniSharp for Windows (.NET 6 / arm64)", "url": "https://roslynomnisharp.blob.core.windows.net/releases/1.39.10/omnisharp-win-arm64-net6.0-1.39.10.zip", "installPath": ".omnisharp/1.39.10-net6.0", "platforms": ["win32"], "architectures": ["arm64"], "installTestPath": "./.omnisharp/1.39.10-net6.0/OmniSharp.dll", "platformId": "win-arm64", "isFramework": false, "integrity": "433F9B360CAA7B4DDD85C604D5C5542C1A718BCF2E71B2BCFC7526E6D41F4E8F", "dotnet_version": "6", "binaryName": "OmniSharp.exe"}, {"id": "OmniSharp", "description": "OmniSharp for OSX (Mono / x64)", "url": "https://roslynomnisharp.blob.core.windows.net/releases/1.39.10/omnisharp-osx-1.39.10.zip", "installPath": ".omnisharp/1.39.10", "platforms": ["darwin"], "architectures": ["x86_64", "arm64"], "binaries": ["./mono.osx", "./run"], "installTestPath": "./.omnisharp/1.39.10/run", "platformId": "osx", "isFramework": true, "integrity": "2CC42F0EC7C30CFA8858501D12ECB6FB685A1FCFB8ECB35698A4B12406551968", "dotnet_version": "mono"}, {"id": "OmniSharp", "description": "OmniSharp for OSX (.NET 6 / x64)", "url": "https://roslynomnisharp.blob.core.windows.net/releases/1.39.10/omnisharp-osx-x64-net6.0-1.39.10.zip", "installPath": ".omnisharp/1.39.10-net6.0", "platforms": ["darwin"], "architectures": ["x86_64"], "installTestPath": "./.omnisharp/1.39.10-net6.0/OmniSharp.dll", "platformId": "osx-x64", "isFramework": false, "integrity": "C9D6E9F2C839A66A7283AE6A9EC545EE049B48EB230D33E91A6322CB67FF9D97", "dotnet_version": "6"}, {"id": "OmniSharp", "description": "OmniSharp for OSX (.NET 6 / arm64)", "url": "https://roslynomnisharp.blob.core.windows.net/releases/1.39.10/omnisharp-osx-arm64-net6.0-1.39.10.zip", "installPath": ".omnisharp/1.39.10-net6.0", "platforms": ["darwin"], "architectures": ["arm64"], "installTestPath": "./.omnisharp/1.39.10-net6.0/OmniSharp.dll", "platformId": "osx-arm64", "isFramework": false, "integrity": "851350F52F83E3BAD5A92D113E4B9882FCD1DEB16AA84FF94B6F2CEE3C70051E", "dotnet_version": "6"}, {"id": "OmniSharp", "description": "OmniSharp for Linux (Mono / x86)", "url": "https://roslynomnisharp.blob.core.windows.net/releases/1.39.10/omnisharp-linux-x86-1.39.10.zip", "installPath": ".omnisharp/1.39.10", "platforms": ["linux"], "architectures": ["x86", "i686"], "binaries": ["./mono.linux-x86", "./run"], "installTestPath": "./.omnisharp/1.39.10/run", "platformId": "linux-x86", "isFramework": true, "integrity": "474B1CDBAE64CFEC655FB6B0659BCE481023C48274441C72991E67B6E13E56A1", "dotnet_version": "mono"}, {"id": "OmniSharp", "description": "OmniSharp for Linux (Mono / x64)", "url": "https://roslynomnisharp.blob.core.windows.net/releases/1.39.10/omnisharp-linux-x64-1.39.10.zip", "installPath": ".omnisharp/1.39.10", "platforms": ["linux"], "architectures": ["x86_64"], "binaries": ["./mono.linux-x86_64", "./run"], "installTestPath": "./.omnisharp/1.39.10/run", "platformId": "linux-x64", "isFramework": true, "integrity": "FB4CAA47343265100349375D79DBCCE1868950CED675CB07FCBE8462EDBCDD37", "dotnet_version": "mono"}, {"id": "OmniSharp", "description": "OmniSharp for Linux (.NET 6 / x64)", "url": "https://roslynomnisharp.blob.core.windows.net/releases/1.39.10/omnisharp-linux-x64-net6.0-1.39.10.zip", "installPath": ".omnisharp/1.39.10-net6.0", "platforms": ["linux"], "architectures": ["x86_64"], "installTestPath": "./.omnisharp/1.39.10-net6.0/OmniSharp.dll", "platformId": "linux-x64", "isFramework": false, "integrity": "0926D3BEA060BF4373356B2FC0A68C10D0DE1B1150100B551BA5932814CE51E2", "dotnet_version": "6", "binaryName": "OmniSharp"}, {"id": "OmniSharp", "description": "OmniSharp for Linux (Mono / arm64)", "url": "https://roslynomnisharp.blob.core.windows.net/releases/1.39.10/omnisharp-linux-arm64-1.39.10.zip", "installPath": ".omnisharp/1.39.10", "platforms": ["linux"], "architectures": ["arm64"], "binaries": ["./mono.linux-arm64", "./run"], "installTestPath": "./.omnisharp/1.39.10/run", "platformId": "linux-arm64", "isFramework": true, "integrity": "478F3594DFD0167E9A56E36F0364A86C73F8132A3E7EA916CA1419EFE141D2CC", "dotnet_version": "mono"}, {"id": "OmniSharp", "description": "OmniSharp for Linux (.NET 6 / arm64)", "url": "https://roslynomnisharp.blob.core.windows.net/releases/1.39.10/omnisharp-linux-arm64-net6.0-1.39.10.zip", "installPath": ".omnisharp/1.39.10-net6.0", "platforms": ["linux"], "architectures": ["arm64"], "installTestPath": "./.omnisharp/1.39.10-net6.0/OmniSharp.dll", "platformId": "linux-arm64", "isFramework": false, "integrity": "6FB6A572043A74220A92F6C19C7BB0C3743321C7563A815FD2702EF4FA7D688E", "dotnet_version": "6"}, {"id": "OmniSharp", "description": "OmniSharp for Linux musl (.NET 6 / x64)", "url": "https://roslynomnisharp.blob.core.windows.net/releases/1.39.10/omnisharp-linux-musl-x64-net6.0-1.39.10.zip", "installPath": ".omnisharp/1.39.10-net6.0", "platforms": ["linux-musl"], "architectures": ["x86_64"], "installTestPath": "./.omnisharp/1.39.10-net6.0/OmniSharp.dll", "platformId": "linux-musl-x64", "isFramework": false, "integrity": "6BFDA3AD11DBB0C6514B86ECC3E1597CC41C6E309B7575F7C599E07D9E2AE610", "dotnet_version": "6"}, {"id": "OmniSharp", "description": "OmniSharp for Linux musl (.NET 6 / arm64)", "url": "https://roslynomnisharp.blob.core.windows.net/releases/1.39.10/omnisharp-linux-musl-arm64-net6.0-1.39.10.zip", "installPath": ".omnisharp/1.39.10-net6.0", "platforms": ["linux-musl"], "architectures": ["arm64"], "installTestPath": "./.omnisharp/1.39.10-net6.0/OmniSharp.dll", "platformId": "linux-musl-arm64", "isFramework": false, "integrity": "DA63619EA024EB9BBF6DB5A85C6150CAB5C0BD554544A3596ED1B17F926D6875", "dotnet_version": "6"}, {"id": "RazorOmnisharp", "description": "Razor Language Server for OmniSharp (Windows / x64)", "url": "https://download.visualstudio.microsoft.com/download/pr/aee63398-023f-48db-bba2-30162c68f0c4/8d42e62ea4051381c219b3e31bc4eced/razorlanguageserver-win-x64-7.0.0-preview.23363.1.zip", "installPath": ".<PERSON><PERSON><PERSON><PERSON><PERSON>", "platforms": ["win32"], "architectures": ["x86_64"], "platformId": "win-x64", "dll_path": "OmniSharpPlugin/Microsoft.AspNetCore.Razor.OmniSharpPlugin.dll"}, {"id": "RazorOmnisharp", "description": "Razor Language Server for OmniSharp (Windows / x86)", "url": "https://download.visualstudio.microsoft.com/download/pr/aee63398-023f-48db-bba2-30162c68f0c4/e440c4f3a4a96334fe177513935fa010/razorlanguageserver-win-x86-7.0.0-preview.23363.1.zip", "installPath": ".<PERSON><PERSON><PERSON><PERSON><PERSON>", "platforms": ["win32"], "architectures": ["x86"], "platformId": "win-x86", "dll_path": "OmniSharpPlugin/Microsoft.AspNetCore.Razor.OmniSharpPlugin.dll"}, {"id": "RazorOmnisharp", "description": "Razor Language Server for OmniSharp (Windows / ARM64)", "url": "https://download.visualstudio.microsoft.com/download/pr/aee63398-023f-48db-bba2-30162c68f0c4/4ef26e45cf32fe8d51c0e7dd21f1fef6/razorlanguageserver-win-arm64-7.0.0-preview.23363.1.zip", "installPath": ".<PERSON><PERSON><PERSON><PERSON><PERSON>", "platforms": ["win32"], "architectures": ["arm64"], "platformId": "win-arm64", "dll_path": "OmniSharpPlugin/Microsoft.AspNetCore.Razor.OmniSharpPlugin.dll"}, {"id": "RazorOmnisharp", "description": "Razor Language Server for OmniSharp (Linux / x64)", "url": "https://download.visualstudio.microsoft.com/download/pr/aee63398-023f-48db-bba2-30162c68f0c4/6d4e23a3c7cf0465743950a39515a716/razorlanguageserver-linux-x64-7.0.0-preview.23363.1.zip", "installPath": ".<PERSON><PERSON><PERSON><PERSON><PERSON>", "platforms": ["linux"], "architectures": ["x86_64"], "binaries": ["./rzls"], "platformId": "linux-x64", "dll_path": "OmniSharpPlugin/Microsoft.AspNetCore.Razor.OmniSharpPlugin.dll"}, {"id": "RazorOmnisharp", "description": "Razor Language Server for OmniSharp (Linux ARM64)", "url": "https://download.visualstudio.microsoft.com/download/pr/aee63398-023f-48db-bba2-30162c68f0c4/85deebd44647ebf65724cc291d722283/razorlanguageserver-linux-arm64-7.0.0-preview.23363.1.zip", "installPath": ".<PERSON><PERSON><PERSON><PERSON><PERSON>", "platforms": ["linux"], "architectures": ["arm64"], "binaries": ["./rzls"], "platformId": "linux-arm64"}, {"id": "RazorOmnisharp", "description": "Razor Language Server for OmniSharp (Linux musl / x64)", "url": "https://download.visualstudio.microsoft.com/download/pr/aee63398-023f-48db-bba2-30162c68f0c4/4f0caa94ae182785655efb15eafcef23/razorlanguageserver-linux-musl-x64-7.0.0-preview.23363.1.zip", "installPath": ".<PERSON><PERSON><PERSON><PERSON><PERSON>", "platforms": ["linux-musl"], "architectures": ["x86_64"], "binaries": ["./rzls"], "platformId": "linux-musl-x64"}, {"id": "RazorOmnisharp", "description": "Razor Language Server for OmniSharp (Linux musl ARM64)", "url": "https://download.visualstudio.microsoft.com/download/pr/aee63398-023f-48db-bba2-30162c68f0c4/0a24828206a6f3b4bc743d058ef88ce7/razorlanguageserver-linux-musl-arm64-7.0.0-preview.23363.1.zip", "installPath": ".<PERSON><PERSON><PERSON><PERSON><PERSON>", "platforms": ["linux-musl"], "architectures": ["arm64"], "binaries": ["./rzls"], "platformId": "linux-musl-arm64"}, {"id": "RazorOmnisharp", "description": "Razor Language Server for OmniSharp (macOS / x64)", "url": "https://download.visualstudio.microsoft.com/download/pr/aee63398-023f-48db-bba2-30162c68f0c4/2afcafaf41082989efcc10405abb9314/razorlanguageserver-osx-x64-7.0.0-preview.23363.1.zip", "installPath": ".<PERSON><PERSON><PERSON><PERSON><PERSON>", "platforms": ["darwin"], "architectures": ["x86_64"], "binaries": ["./rzls"], "platformId": "osx-x64"}, {"id": "RazorOmnisharp", "description": "Razor Language Server for OmniSharp (macOS ARM64)", "url": "https://download.visualstudio.microsoft.com/download/pr/aee63398-023f-48db-bba2-30162c68f0c4/8bf2ed2f00d481a5987e3eb5165afddd/razorlanguageserver-osx-arm64-7.0.0-preview.23363.1.zip", "installPath": ".<PERSON><PERSON><PERSON><PERSON><PERSON>", "platforms": ["darwin"], "architectures": ["arm64"], "binaries": ["./rzls"], "platformId": "osx-arm64"}]}