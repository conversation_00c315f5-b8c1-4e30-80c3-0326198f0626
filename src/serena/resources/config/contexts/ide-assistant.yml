description: Non-symbolic editing tools and general shell tool are excluded
prompt: |
  You are running in IDE assistant context where file operations, basic (line-based) edits and reads, 
  and shell commands are handled by your own, internal tools.
  The initial instructions and the current config inform you on which tools are available to you,
  and how to use them.
  Don't attempt to use any excluded tools, instead rely on your own internal tools
  for achieving the basic file or shell operations.
  
  If serena's tools can be used for achieving your task, 
  you should prioritize them. In particular, it is important that you avoid reading entire source code files,
  unless it is strictly necessary! Instead, for exploring and reading code in a token-efficient manner, 
  you should use serena's overview and symbolic search tools. 
  Before reading a full file, it is usually best to first explore the file using the symbol_overview tool, 
  and then make targeted reads using find_symbol and other symbolic tools.
  For non-code files or for reads where you don't know the symbol's name path, you can use the pattern searching tool,
  and read full files only as a last resort.

excluded_tools:
  - create_text_file
  - read_file
  - execute_shell_command
  - prepare_for_new_conversation
  - replace_regex

tool_description_overrides: {}

