JDTLS日志 - 2025-10-09 16:03:10
==================================================
[stderr] OpenJDK 64-Bit Server VM warning: Options -Xverify:none and -noverify were deprecated in JDK 13 and will likely be removed in a future release.
[stderr] Oct 09, 2025 4:02:04 PM org.apache.aries.spifly.BaseActivator log
[stderr] INFO: Registered provider ch.qos.logback.classic.spi.LogbackServiceProvider of service org.slf4j.spi.SLF4JServiceProvider in bundle ch.qos.logback.classic
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"window/logMessage","params":{"type":3,"message":"Oct 9, 2025, 4:02:06 PM Started org.eclipse.buildship.core 59ms"}}Content-Length: 137
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"window/logMessage","params":{"type":3,"message":"Oct 9, 2025, 4:02:06 PM Started org.eclipse.m2e.core 0ms"}}Content-Length: 127
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"window/logMessage","params":{"type":3,"message":"Oct 9, 2025, 4:02:06 PM Main thread is waiting"}}Content-Length: 128
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"window/logMessage","params":{"type":3,"message":"Oct 9, 2025, 4:02:06 PM \u003e\u003e initialize"}}Content-Length: 158
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"window/logMessage","params":{"type":3,"message":"Oct 9, 2025, 4:02:06 PM Initializing Java Language Server 1.51.0.202510022025"}}Content-Length: 146
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"window/logMessage","params":{"type":3,"message":"Oct 9, 2025, 4:02:08 PM ProjectRegistryRefreshJob finished 1731ms"}}Content-Length: 124
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"window/logMessage","params":{"type":3,"message":"Oct 9, 2025, 4:02:08 PM Static Commands: []"}}Content-Length: 1069
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"window/logMessage","params":{"type":3,"message":"Oct 9, 2025, 4:02:08 PM Non-Static Commands: [java.project.import, java.project.changeImportedProjects, java.navigate.openTypeHierarchy, java.project.resolveStackTraceLocation, java.edit.handlePasteEvent, java.edit.stringFormatting, java.project.getSettings, java.project.resolveWorkspaceSymbol, java.project.upgradeGradle, java.project.createModuleInfo, java.vm.getAllInstalls, java.edit.organizeImports, java.project.refreshDiagnostics, java.project.removeFromSourcePath, java.project.listSourcePaths, java.project.updateSettings, java.project.getAll, java.reloadBundles, java.project.isTestFile, java.project.resolveText, java.project.getClasspaths, java.navigate.resolveTypeHierarchy, java.getTroubleshootingInfo, java.edit.smartSemicolonDetection, java.project.updateSourceAttachment, java.project.updateClassPaths, java.decompile, java.protobuf.generateSources, java.project.resolveSourceAttachment, java.project.updateJdk, java.project.addToSourcePath, java.completion.onDidSelect]"}}Content-Length: 93
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"language/status","params":{"type":"Starting","message":"Init..."}}Content-Length: 118
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"language/status","params":{"type":"Starting","message":"0% Starting Java Language Server"}}Content-Length: 145
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"window/logMessage","params":{"type":3,"message":"Oct 9, 2025, 4:02:08 PM RepositoryRegistryUpdateJob finished 1ms"}}Content-Length: 2743
[stdout] 
[stdout] {"jsonrpc":"2.0","id":1,"result":{"capabilities":{"textDocumentSync":{"openClose":true,"change":2,"save":{"includeText":true}},"hoverProvider":true,"completionProvider":{"resolveProvider":true,"triggerCharacters":[".","@","#","*"," "]},"signatureHelpProvider":{"triggerCharacters":["(",","]},"definitionProvider":true,"typeDefinitionProvider":true,"implementationProvider":true,"referencesProvider":true,"documentHighlightProvider":true,"documentSymbolProvider":true,"workspaceSymbolProvider":true,"codeActionProvider":{"codeActionKinds":[],"resolveProvider":false},"codeLensProvider":{"resolveProvider":true},"documentFormattingProvider":true,"documentRangeFormattingProvider":true,"documentOnTypeFormattingProvider":{"firstTriggerCharacter":";","moreTriggerCharacter":["\n","}"]},"renameProvider":{"prepareProvider":true},"foldingRangeProvider":true,"declarationProvider":true,"executeCommandProvider":{"commands":["java.project.import","java.project.changeImportedProjects","java.navigate.openTypeHierarchy","java.project.resolveStackTraceLocation","java.edit.handlePasteEvent","java.edit.stringFormatting","java.project.getSettings","java.project.resolveWorkspaceSymbol","java.project.upgradeGradle","java.project.createModuleInfo","java.vm.getAllInstalls","java.edit.organizeImports","java.project.refreshDiagnostics","java.project.removeFromSourcePath","java.project.listSourcePaths","java.project.updateSettings","java.project.getAll","java.reloadBundles","java.project.isTestFile","java.project.resolveText","java.project.getClasspaths","java.navigate.resolveTypeHierarchy","java.getTroubleshootingInfo","java.edit.smartSemicolonDetection","java.project.updateSourceAttachment","java.project.updateClassPaths","java.decompile","java.protobuf.generateSources","java.project.resolveSourceAttachment","java.project.updateJdk","java.project.addToSourcePath","java.completion.onDidSelect"]},"workspace":{"workspaceFolders":{"supported":true,"changeNotifications":true}},"typeHierarchyProvider":true,"callHierarchyProvider":true,"selectionRangeProvider":true,"semanticTokensProvider":{"legend":{"tokenTypes":["namespace","class","interface","enum","enumMember","type","typeParameter","method","property","variable","parameter","modifier","keyword","annotation","annotationMember","record","recordComponent"],"tokenModifiers":["abstract","static","readonly","deprecated","declaration","documentation","public","private","protected","native","generic","typeArgument","importDeclaration","constructor"]},"range":false,"full":{"delta":false},"documentSelector":[{"language":"java","scheme":"file"},{"language":"java","scheme":"jdt"}]},"inlayHintProvider":true},"serverInfo":{"name":"JDT Language Server (Standard)","version":"1.51.0-SNAPSHOT"}}}Content-Length: 129
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"window/logMessage","params":{"type":3,"message":"Oct 9, 2025, 4:02:08 PM \u003e\u003e initialized"}}Content-Length: 131
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"window/logMessage","params":{"type":3,"message":"Oct 9, 2025, 4:02:08 PM Importing Maven project(s)"}}Content-Length: 149
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"language/status","params":{"type":"Starting","message":"24% Starting Java Language Server - Importing project test_repo"}}Content-Length: 93
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"language/status","params":{"type":"ProjectStatus","message":"OK"}}Content-Length: 142
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"language/status","params":{"type":"Starting","message":"100% Starting Java Language Server - Refreshing projects"}}Content-Length: 135
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"window/logMessage","params":{"type":3,"message":"Oct 9, 2025, 4:02:08 PM Workspace initialized in 240ms"}}Content-Length: 90
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"language/status","params":{"type":"Started","message":"Ready"}}Content-Length: 142
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"language/status","params":{"type":"Starting","message":"100% Starting Java Language Server - Refreshing projects"}}Content-Length: 145
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"window/logMessage","params":{"type":3,"message":"Oct 9, 2025, 4:02:08 PM \u003e\u003e initialization job finished"}}Content-Length: 185
[stdout] 
[stdout] {"jsonrpc":"2.0","id":"1","method":"client/registerCapability","params":{"registrations":[{"id":"fe95f2d3-2c68-432f-b52f-d85247f05c0d","method":"workspace/didChangeWorkspaceFolders"}]}}Content-Length: 102
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"language/status","params":{"type":"ServiceReady","message":"ServiceReady"}}Content-Length: 137
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"window/logMessage","params":{"type":3,"message":"Oct 9, 2025, 4:02:08 PM \u003e\u003e build jobs finished"}}Content-Length: 140
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"window/logMessage","params":{"type":3,"message":"Oct 9, 2025, 4:02:08 PM \u003e\u003e registerWatchers\u0027"}}Content-Length: 150
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"window/logMessage","params":{"type":3,"message":"Oct 9, 2025, 4:02:11 PM Creating the Java project jdt.ls-java-project"}}Content-Length: 159
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"window/logMessage","params":{"type":3,"message":"Oct 9, 2025, 4:02:11 PM Finished creating the Java project jdt.ls-java-project"}}Content-Length: 128
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"window/logMessage","params":{"type":3,"message":"Oct 9, 2025, 4:02:13 PM Reconciled 1. Took 0 ms"}}Content-Length: 1143
[stdout] 
[stdout] {"jsonrpc":"2.0","id":2,"result":[{"name":"name","kind":8,"location":{"uri":"file:///home/<USER>/Codebase/gerrit/serena/test/resources/repos/java/test_repo/src/main/java/test_repo/Model.java","range":{"start":{"line":3,"character":19},"end":{"line":3,"character":23}}},"containerName":"Model"},{"name":"Model(String)","kind":9,"location":{"uri":"file:///home/<USER>/Codebase/gerrit/serena/test/resources/repos/java/test_repo/src/main/java/test_repo/Model.java","range":{"start":{"line":5,"character":11},"end":{"line":5,"character":16}}},"containerName":"Model"},{"name":"getName()","kind":6,"location":{"uri":"file:///home/<USER>/Codebase/gerrit/serena/test/resources/repos/java/test_repo/src/main/java/test_repo/Model.java","range":{"start":{"line":9,"character":18},"end":{"line":9,"character":25}}},"containerName":"Model"},{"name":"Model","kind":5,"location":{"uri":"file:///home/<USER>/Codebase/gerrit/serena/test/resources/repos/java/test_repo/src/main/java/test_repo/Model.java","range":{"start":{"line":2,"character":13},"end":{"line":2,"character":18}}},"containerName":"Model.java"}]}Content-Length: 134
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"window/logMessage","params":{"type":3,"message":"Oct 9, 2025, 4:02:14 PM begin problem for /Model.java"}}Content-Length: 140
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"window/logMessage","params":{"type":3,"message":"Oct 9, 2025, 4:02:14 PM 1 problems reported for /Model.java"}}Content-Length: 416
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"textDocument/publishDiagnostics","params":{"uri":"file:///home/<USER>/Codebase/gerrit/serena/test/resources/repos/java/test_repo/src/main/java/test_repo/Model.java","diagnostics":[{"range":{"start":{"line":0,"character":0},"end":{"line":0,"character":1}},"severity":2,"code":"16","source":"Java","message":"Model.java is a non-project file, only syntax errors are reported"}]}}Content-Length: 129
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"window/logMessage","params":{"type":3,"message":"Oct 9, 2025, 4:02:14 PM Validated 1. Took 136 ms"}}Content-Length: 138
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"window/logMessage","params":{"type":3,"message":"Oct 9, 2025, 4:02:28 PM Clearing problems for /Model.java"}}Content-Length: 222
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"textDocument/publishDiagnostics","params":{"uri":"file:///home/<USER>/Codebase/gerrit/serena/test/resources/repos/java/test_repo/src/main/java/test_repo/Model.java","diagnostics":[]}}
