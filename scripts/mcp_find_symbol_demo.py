"""
MCP Client Demonstration for find_symbol Tool

This script demonstrates how to connect to a running Serena MCP server
and use the find_symbol tool to search for symbols in the codebase.

Usage:
    python scripts/mcp_find_symbol_demo.py

The MCP server should be running at http://0.0.0.0:8000
If the server requires an API key, set it in the MCPClient constructor.
"""

import asyncio
import json
import os
import sys
from pprint import pprint
from typing import Any, Dict, List, Optional

import httpx
from mcp import ClientSession
from mcp.client import sse


class MCPClient:
    """
    An MCP client for connecting to Serena MCP server using the official mcp library.
    """
    
    def __init__(self, server_url: str = "http://0.0.0.0:8000", api_key: Optional[str] = None):
        """
        Initialize the MCP client.
        
        :param server_url: URL of the MCP server
        :param api_key: Optional API key for authentication
        """
        self.server_url = server_url.rstrip('/')
        self.api_key = api_key
        
    async def run_demonstration(self):
        """
        Run the complete demonstration with a single long-lived connection.
        """
        print("=" * 60)
        print("MCP find_symbol Tool Demonstration")
        print("=" * 60)
        print()
        
        # Prepare headers with API key if provided
        headers = {}
        if self.api_key:
            headers["Authorization"] = f"Bearer {self.api_key}"
        
        # Create a custom HTTP client factory that bypasses proxy for localhost
        def create_local_http_client(headers=None, timeout=None, auth=None):
            # Bypass proxy for localhost connections
            proxies = {}
            if "0.0.0.0" in self.server_url or "localhost" in self.server_url or "127.0.0.1" in self.server_url:
                proxies = {"http://": None, "https://": None}
            
            return httpx.AsyncClient(
                headers=headers,
                timeout=timeout or httpx.Timeout(30.0),
                auth=auth,
                proxies=proxies,
                follow_redirects=True
            )
        
        print(f"🔗 Attempting to connect to {self.server_url}/sse...")
        
        try:
            # Create SSE client and connect - keep the context manager alive for the entire demonstration
            async with sse.sse_client(
                url=f"{self.server_url}/sse",
                headers=headers,
                timeout=30.0,
                sse_read_timeout=300.0,
                httpx_client_factory=create_local_http_client
            ) as (read_stream, write_stream):
                # Create client session
                async with ClientSession(read_stream, write_stream) as session:
                    # Initialize the session
                    await session.initialize()
                    print("✅ Connected to MCP server successfully!")
                    print()
                    
                    # Run demonstration scenarios
                    await self._run_scenarios(session)
                    
        except Exception as e:
            print(f"❌ Failed to connect to MCP server: {e}")
            import traceback
            traceback.print_exc()
            return
    
    async def _run_scenarios(self, session: ClientSession):
        """
        Run demonstration scenarios with the active session.
        
        :param session: Active ClientSession instance
        """
        # First, let's test what project the server is working with
        print("🔍 Testing server project context...")
        print("-" * 40)
        
        # # Test with common patterns that might exist in any project
        # test_patterns = [
        #     {"name": "Python files", "params": {"name_path": ".py", "substring_matching": True}},
        #     {"name": "Test files", "params": {"name_path": "test", "substring_matching": True}},
        #     {"name": "Main functions", "params": {"name_path": "main", "substring_matching": True}},
        #     {"name": "Classes", "params": {"name_path": "class", "substring_matching": True}},
        #     {"name": "Functions", "params": {"name_path": "def", "substring_matching": True}},
        #     {"name": "All symbols", "params": {"name_path": "", "substring_matching": True}},
        # ]
        
        # for test in test_patterns:
        #     print(f"Testing: {test['name']}")
        #     symbols = await self._find_symbol(session, **test['params'])
        #     print(self._format_symbol_result(symbols))
        #     print()
        
        # Demonstration scenarios with more realistic patterns
        print("🎯 Running demonstration scenarios...")
        print("=" * 60)
        print()
        
        scenarios = [
            {
                "name": "Simple symbol name search",
                "description": "Search for symbols containing 'main'",
                "params": {"name_path": "history", "substring_matching": False}
            },
            # {
            #     "name": "Name path pattern (relative)",
            #     "description": "Search for methods within classes using 'class/method' pattern",
            #     "params": {"name_path": "class/method"}
            # },
            # {
            #     "name": "Name path pattern (absolute)",
            #     "description": "Search for top-level classes using '/class' pattern",
            #     "params": {"name_path": "/class"}
            # },
            # {
            #     "name": "Directory restriction",
            #     "description": "Search only in current directory",
            #     "params": {"name_path": "", "relative_path": ".", "substring_matching": True}
            # },
            # {
            #     "name": "Symbol kind filtering (functions only)",
            #     "description": "Search only for functions (kind 12)",
            #     "params": {"name_path": "", "include_kinds": [12], "substring_matching": True}
            # },
            # {
            #     "name": "Include source code bodies",
            #     "description": "Search for 'main' methods with source code included",
            #     "params": {"name_path": "main", "include_body": True, "substring_matching": True}
            # }
        ]
        
        for i, scenario in enumerate(scenarios, 1):
            print(f"Scenario {i}: {scenario['name']}")
            print(f"Description: {scenario['description']}")
            print(f"Parameters: {scenario['params']}")
            print("-" * 40)
            
            try:
                symbols = await self._find_symbol(session, **scenario['params'])
                print(self._format_symbol_result(symbols))
            except Exception as e:
                print(f"❌ Error: {e}")
                print()
            
            print("=" * 60)
            print()
    
    async def _find_symbol(
        self,
        session: ClientSession,
        name_path: str,
        depth: int = 0,
        relative_path: str = "",
        include_body: bool = False,
        include_kinds: Optional[List[int]] = None,
        exclude_kinds: Optional[List[int]] = None,
        substring_matching: bool = False,
        max_answer_chars: int = -1
    ) -> List[Dict[str, Any]]:
        """
        Find symbols using the find_symbol tool.
        
        :param session: Active ClientSession instance
        :param name_path: Pattern for symbol name/path matching
        :param depth: Depth to retrieve descendants (e.g., 1 for class methods)
        :param relative_path: Restrict search to this file or directory
        :param include_body: Include the symbol's source code
        :param include_kinds: List of LSP symbol kind integers to include
        :param exclude_kinds: List of LSP symbol kind integers to exclude
        :param substring_matching: Use substring matching for the last segment
        :param max_answer_chars: Max characters for the JSON result
        :return: List of matching symbols
        """
        # arguments = {
        #     "name_path": name_path,
        #     "depth": depth,
        #     "relative_path": relative_path,
        #     "include_body": include_body,
        #     "substring_matching": substring_matching,
        #     "max_answer_chars": max_answer_chars
        # }
        arguments = {
            "substring_pattern": name_path,
            "restrict_search_to_code_files": True
        }
        
        if include_kinds:
            arguments["include_kinds"] = include_kinds
        if exclude_kinds:
            arguments["exclude_kinds"] = exclude_kinds
        
        print(f"🔧 Calling tool: find_symbol with arguments: {arguments}")
        
        try:
            # result = await session.call_tool("find_symbol", arguments)
            result = await session.call_tool("search_for_pattern", arguments)
            print(f"✅ Tool call successful, result type: {type(result)}")
            result_dict = result.model_dump()
            print(f"📊 Raw result structure: {result_dict}")
            
            # Check if the result contains an error message about project selection
            content = result_dict.get("content", [])
            if content and len(content) > 0:
                first_item = content[0]
                if first_item.get("type") == "text" and "No active project" in first_item.get("text", ""):
                    print(f"⚠️  MCP Server requires project selection: {first_item.get('text')}")
                    return []
                
                # Parse the JSON string from text content
                if first_item.get("type") == "text":
                    try:
                        text_content = first_item.get("text", "")
                        if text_content.startswith("[") and text_content.endswith("]"):
                            symbols = json.loads(text_content)
                            print(f"📋 Parsed {len(symbols)} symbols from JSON")
                            return symbols
                        elif "Error executing tool" in text_content:
                            print(f"❌ Server error: {text_content}")
                            return []
                    except json.JSONDecodeError as e:
                        print(f"❌ Failed to parse JSON response: {e}")
                        print(f"Raw text: {text_content}")
                        return []
            
            return content
        except Exception as e:
            print(f"❌ Error calling tool find_symbol: {e}")
            import traceback
            traceback.print_exc()
            raise
    
    def _format_symbol_result(self, symbols: List[Dict[str, Any]]) -> str:
        """
        Format symbol results for display.
        
        :param symbols: List of symbol dictionaries
        :return: Formatted string with symbol information
        """
        if not symbols:
            return "No symbols found."
        
        result = f"Found {len(symbols)} symbol(s):\n\n"
        
        for i, symbol in enumerate(symbols, 1):
            result += f"Symbol {i}:\n"
            result += f"  Name Path: {symbol.get('name_path', 'N/A')}\n"
            result += f"  Kind: {symbol.get('kind', 'N/A')}\n"
            result += f"  File: {symbol.get('relative_path', 'N/A')}\n"
            
            if 'body_location' in symbol:
                body_loc = symbol['body_location']
                result += f"  Location: Line {body_loc.get('start_line', 'N/A')}-{body_loc.get('end_line', 'N/A')}\n"
            
            if symbol.get('body'):
                body_preview = symbol['body'][:100] + "..." if len(symbol['body']) > 100 else symbol['body']
                result += f"  Body Preview: {body_preview}\n"
            
            result += "\n"
        
        return result


async def main():
    """Main function to run the demonstration."""
    # Create MCP client - try with API key if needed
    api_key = "demo-key"  # Try with the API key mentioned in documentation
    client = MCPClient(server_url="http://0.0.0.0:8000", api_key=api_key)
    
    try:
        await client.run_demonstration()
    except KeyboardInterrupt:
        print("\nDemonstration interrupted by user.")
    except Exception as e:
        print(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())