"""
Simple script to test MCP server endpoints
"""

import requests

def test_endpoints():
    base_url = "http://0.0.0.0:8000"
    session = requests.Session()
    session.trust_env = False  # Bypass proxy
    
    # Test various potential endpoints
    endpoints = [
        "/",
        "/health",
        "/tools",
        "/tools/call",
        "/openapi.json",
        "/docs",
        "/api/tools",
        "/api/tools/call",
        "/v1/tools",
        "/v1/tools/call"
    ]
    
    print("Testing MCP server endpoints...")
    print(f"Base URL: {base_url}")
    print()
    
    for endpoint in endpoints:
        url = base_url + endpoint
        try:
            response = session.get(url, timeout=5)
            print(f"{endpoint}: {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"{endpoint}: Error - {e}")

if __name__ == "__main__":
    test_endpoints()