import subprocess
import json
import threading
import time
import sys
import os
from pathlib import Path

class FinalJDTLSClient:
    def __init__(self, jdtls_command):
        self.process = None
        self.jdtls_command = jdtls_command
        self.responses = {}
        self.is_running = False
        self.request_id = 0
        self.all_output = []  # 存储所有输出用于调试
        
    def start(self):
        """启动JDTLS进程"""
        try:
            self.process = subprocess.Popen(
                self.jdtls_command,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=0,
                encoding='utf-8'
            )
            
            self.is_running = True
            print(f"JDTLS进程已启动 (PID: {self.process.pid})")
            
            # 启动监听线程
            self.stdout_thread = threading.Thread(target=self._listen_stdout, daemon=True)
            self.stderr_thread = threading.Thread(target=self._listen_stderr, daemon=True)
            self.stdout_thread.start()
            self.stderr_thread.start()

            return True
            
        except Exception as e:
            print(f"启动JDTLS进程失败: {e}")
            return False
    
    def _listen_stdout(self):
        """监听stdout并解析LSP消息"""
        print("开始监听JDTLS stdout...")
        buffer = ""

        while self.is_running and self.process and self.process.stdout:
            try:
                line = self.process.stdout.readline()
                if not line:
                    break

                # 打印所有原始输出
                line_stripped = line.strip()
                if line_stripped:
                    print(f"[STDOUT] {line_stripped}")
                    self.all_output.append(f"[STDOUT] {line_stripped}")

                buffer += line

                # 查找并解析所有完整的JSON消息
                while True:
                    # 查找JSON消息的开始
                    start_pos = buffer.find('{"jsonrpc"')
                    if start_pos == -1:
                        break

                    # 从开始位置查找完整的JSON
                    brace_count = 0
                    in_string = False
                    escape_next = False
                    end_pos = start_pos

                    for i, char in enumerate(buffer[start_pos:], start_pos):
                        if escape_next:
                            escape_next = False
                            continue

                        if char == '\\':
                            escape_next = True
                            continue

                        if char == '"' and not escape_next:
                            in_string = not in_string
                            continue

                        if not in_string:
                            if char == '{':
                                brace_count += 1
                            elif char == '}':
                                brace_count -= 1
                                if brace_count == 0:
                                    end_pos = i + 1
                                    break

                    if brace_count == 0:
                        # 找到完整的JSON消息
                        json_content = buffer[start_pos:end_pos]
                        buffer = buffer[end_pos:]

                        try:
                            message = json.loads(json_content)
                            self._handle_message(message)
                        except json.JSONDecodeError:
                            pass
                    else:
                        # JSON不完整，等待更多数据
                        break

            except Exception as e:
                print(f"读取stdout时出错: {e}")
                break

    def _listen_stderr(self):
        """监听stderr输出"""
        print("开始监听JDTLS stderr...")

        while self.is_running and self.process and self.process.stderr:
            try:
                line = self.process.stderr.readline()
                if not line:
                    break

                # 打印所有stderr输出
                line_stripped = line.strip()
                if line_stripped:
                    print(f"[STDERR] {line_stripped}")
                    self.all_output.append(f"[STDERR] {line_stripped}")

            except Exception as e:
                print(f"读取stderr时出错: {e}")
                break
    
    def _handle_message(self, message):
        """处理收到的LSP消息"""
        if "id" in message and "result" in message:
            # 这是一个响应
            self.responses[message["id"]] = message
            print(f"✓ 收到响应 ID {message['id']}")
            print(f"   响应内容: {json.dumps(message, indent=2, ensure_ascii=False)[:500]}...")
        elif "method" in message:
            # 这是一个通知
            method = message.get('method', 'unknown')
            print(f"收到通知: {method}")
            if method in ['textDocument/publishDiagnostics']:
                print(f"   通知内容: {json.dumps(message, indent=2, ensure_ascii=False)[:300]}...")
        else:
            print(f"收到未知消息: {json.dumps(message, indent=2, ensure_ascii=False)[:200]}...")
    
    def send_request(self, request):
        """发送LSP请求"""
        if not self.process or not self.is_running:
            print("错误: JDTLS进程未运行")
            return False
            
        try:
            content = json.dumps(request)
            header = f"Content-Length: {len(content)}\r\n\r\n"
            message = header + content
            self.process.stdin.write(message)
            self.process.stdin.flush()
            print(f"已发送请求 ID {request.get('id', 'N/A')}: {request['method']}")
            return True
        except Exception as e:
            print(f"发送请求时出错: {e}")
            return False
    
    def wait_for_response(self, request_id, timeout=60):
        """等待指定ID的响应"""
        start_time = time.time()
        while time.time() - start_time < timeout:
            if request_id in self.responses:
                return self.responses.pop(request_id)
            time.sleep(0.1)
        return None
    
    def shutdown(self):
        """关闭JDTLS进程"""
        self.is_running = False
        if self.process:
            try:
                self.process.terminate()
                self.process.wait(timeout=5)
            except:
                pass

def create_jdtls_command(workspace_dir):
    """创建JDTLS启动命令"""
    java_home = "/home/<USER>/.serena/language_servers/static/EclipseJDTLS/vscode-java/extension/jre/21.0.7-linux-x86_64"
    jdtls_home = '/home/<USER>/Downloads/jdt/'
    
    java_executable = str(Path(java_home) / 'bin' / 'java')
    jdtls_jar = str(Path(jdtls_home) / 'plugins' / 'org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar')
    
    command = [
        java_executable,
        '-Declipse.application=org.eclipse.jdt.ls.core.id1',
        '-Dosgi.bundles.defaultStartLevel=4',
        '-Declipse.product=org.eclipse.jdt.ls.core.product',
        '-Dlog.protocol=true',
        '-Dlog.level=ALL',
        '-noverify',
        '-Xmx1G',
        '--add-modules=ALL-SYSTEM',
        '--add-opens', 'java.base/java.util=ALL-UNNAMED',
        '--add-opens', 'java.base/java.lang=ALL-UNNAMED',
        '-jar', jdtls_jar,
        '-configuration', str(Path(jdtls_home) / 'config_linux'),
        '-data', workspace_dir,
        '-verbose'
    ]
    
    return command

def find_java_files(workspace_dir):
    """查找workspace下的所有Java文件"""
    java_files = []
    for root, dirs, files in os.walk(workspace_dir):
        for file in files:
            if file.endswith('.java'):
                java_files.append(os.path.join(root, file))
    return java_files

def get_symbol_kind_name(kind):
    """将符号类型数字转换为可读名称"""
    symbol_kinds = {
        1: "File", 2: "Module", 3: "Namespace", 4: "Package", 5: "Class",
        6: "Method", 7: "Property", 8: "Field", 9: "Constructor", 10: "Enum",
        11: "Interface", 12: "Function", 13: "Variable", 14: "Constant",
        15: "String", 16: "Number", 17: "Boolean", 18: "Array", 19: "Object",
        20: "Key", 21: "Null", 22: "EnumMember", 23: "Struct", 24: "Event",
        25: "Operator", 26: "TypeParameter"
    }
    return symbol_kinds.get(kind, f"Unknown({kind})")

def parse_and_display_symbols(symbols, indent=0):
    """解析并显示符号信息"""
    if not symbols:
        return
    
    for symbol in symbols:
        name = symbol.get('name', 'Unknown')
        kind = symbol.get('kind', 0)
        kind_name = get_symbol_kind_name(kind)
        
        # 获取位置信息
        if 'range' in symbol:
            range_info = symbol['range']
        elif 'location' in symbol and 'range' in symbol['location']:
            range_info = symbol['location']['range']
        else:
            range_info = {}
        
        start_line = range_info.get('start', {}).get('line', 0) + 1
        start_char = range_info.get('start', {}).get('character', 0)
        end_line = range_info.get('end', {}).get('line', 0) + 1
        end_char = range_info.get('end', {}).get('character', 0)
        
        indent_str = "  " * indent
        print(f"{indent_str}[{kind_name}] {name} (行 {start_line}:{start_char} - {end_line}:{end_char})")
        
        detail = symbol.get('detail', '')
        if detail:
            print(f"{indent_str}  详细: {detail}")
        
        children = symbol.get('children', [])
        if children:
            parse_and_display_symbols(children, indent + 1)

def main():
    # 配置参数
    current_dir = os.path.dirname(os.path.abspath(__file__))
    workspace_dir = os.path.join(current_dir, "test/resources/repos/java/test_repo")
    
    if not os.path.exists(workspace_dir):
        workspace_dir = "/home/<USER>/Codebase/gerrit/PATCHER"
        print(f"测试目录不存在，使用: {workspace_dir}")
    else:
        print(f"使用测试Java项目: {workspace_dir}")
    
    # 创建JDTLS命令
    jdtls_command = create_jdtls_command(workspace_dir)
    
    # 创建客户端
    client = FinalJDTLSClient(jdtls_command)
    
    try:
        # 启动JDTLS
        if not client.start():
            sys.exit(1)
        
        # 等待进程初始化
        print("等待JDTLS进程初始化...")
        time.sleep(5)
        
        # 初始化请求
        initialize_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "processId": os.getpid(),
                "clientInfo": {"name": "Python JDTLS Client", "version": "1.0.0"},
                "rootUri": f"file://{workspace_dir}",
                "capabilities": {
                    "workspace": {"configuration": True, "workspaceFolders": True},
                    "textDocument": {
                        "completion": {"completionItem": {"snippetSupport": True}}
                    }
                },
                "trace": "verbose"
            }
        }
        
        if client.send_request(initialize_request):
            response = client.wait_for_response(1, timeout=30)
            if response:
                print("✓ 初始化成功")
            else:
                print("✗ 初始化失败")
                return
        
        # 发送initialized通知
        client.send_request({"jsonrpc": "2.0", "method": "initialized", "params": {}})
        
        # 等待服务器准备就绪
        print("等待服务器准备就绪...")
        time.sleep(15)  # 增加等待时间

        print(f"\n=== 到目前为止的所有输出 ===")
        for output_line in client.all_output[-20:]:  # 显示最近20行
            print(output_line)
        
        # 查找Java文件
        java_files = find_java_files(workspace_dir)
        if not java_files:
            print("未找到Java文件")
            return
        
        test_java_file = java_files[0]
        print(f"选择测试文件: {test_java_file}")
        
        # 读取文件内容
        with open(test_java_file, 'r', encoding='utf-8') as f:
            file_content = f.read()
        
        file_uri = f"file://{test_java_file}"
        
        # 发送 didOpen 通知
        print(f"\n=== 发送 didOpen 通知 ===")
        did_open_notification = {
            "jsonrpc": "2.0",
            "method": "textDocument/didOpen",
            "params": {
                "textDocument": {
                    "uri": file_uri,
                    "languageId": "java",
                    "version": 1,
                    "text": file_content
                }
            }
        }
        client.send_request(did_open_notification)
        time.sleep(5)  # 等待文件处理
        
        # 发送 documentSymbol 请求
        print(f"\n=== 发送 documentSymbol 请求 ===")
        document_symbol_request = {
            "jsonrpc": "2.0",
            "id": 2,
            "method": "textDocument/documentSymbol",
            "params": {"textDocument": {"uri": file_uri}}
        }
        
        if client.send_request(document_symbol_request):
            print("等待 documentSymbol 响应...")

            # 等待响应，每5秒检查一次并显示状态
            for wait_round in range(6):  # 总共等待30秒
                time.sleep(5)
                print(f"等待中... ({(wait_round + 1) * 5}秒)")

                # 显示最新的输出
                if client.all_output:
                    print(f"最新输出: {client.all_output[-1]}")

                # 检查是否收到响应
                if 2 in client.responses:
                    response = client.responses.pop(2)
                    print(f"\n=== 文档符号响应 ===")
                    print(json.dumps(response, indent=2, ensure_ascii=False))

                    if "result" in response:
                        print(f"\n=== 解析的符号信息 ===")
                        parse_and_display_symbols(response["result"])
                    break
            else:
                print("未收到 documentSymbol 响应")
                print(f"\n=== 最近的所有输出 ===")
                for output_line in client.all_output[-10:]:
                    print(output_line)
        
        # 发送 didClose 通知
        client.send_request({
            "jsonrpc": "2.0",
            "method": "textDocument/didClose",
            "params": {"textDocument": {"uri": file_uri}}
        })

        # 最后再检查一次是否有延迟到达的响应
        time.sleep(5)
        if 2 in client.responses:
            response = client.responses.pop(2)
            print(f"\n=== 延迟到达的文档符号响应 ===")
            print(json.dumps(response, indent=2, ensure_ascii=False))

            if "result" in response:
                print(f"\n=== 解析的符号信息 ===")
                parse_and_display_symbols(response["result"])
        
    except Exception as e:
        print(f"运行过程中出错: {e}")
    finally:
        client.shutdown()

if __name__ == "__main__":
    main()
