name: Publish Python Package

on:
  release:
    types: [created]

  workflow_dispatch:
    inputs:
      tag:
        description: 'Tag name for the release (e.g., v0.1.0)'
        required: true
        default: 'v0.1.0'

env:
  # Set this to true manually in the GitHub workflow UI if you want to publish to PyPI
  # Will always publish to testpypi
  PUBLISH_TO_PYPI: true

jobs:
  publish:
    name: Publish the serena-agent package
    runs-on: ubuntu-latest

    permissions:
      id-token: write  # Required for trusted publishing
      contents: write  # Required for updating artifact

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install the latest version of uv
        uses: astral-sh/setup-uv@v6
        with:
          version: "latest"

      - name: Build package
        run: uv build

      - name: Upload artifacts to GitHub Release
        if: env.PUBLISH_TO_PYPI == 'true'
        uses: softprops/action-gh-release@v2
        with:
          tag_name: ${{ github.event.inputs.tag || github.ref_name }}
          files: |
            dist/*.tar.gz
            dist/*.whl

      - name: Publish to TestPyPI
        run: uv publish --index testpypi

      - name: Publish to PyPI (conditional)
        if: env.PUBLISH_TO_PYPI == 'true'
        run: uv publish
