import json

json_request = {
    "jsonrpc": "2.0",
    "id": 1,
    "method": "initialize",
    "params": {
        "processId": 384955,
        "clientInfo": {
            "name": "Python JDTLS Client",
            "version": "1.0.0"
        },
        "rootUri": "file:///home/<USER>/Codebase/gerrit/patcher",
        "capabilities": {
            "workspace": {
                "configuration": True,
                "workspaceFolders": True
            },
            "textDocument": {
                "completion": {
                    "completionItem": {
                        "snippetSupport": True
                    }
                }
            }
        },
        "trace": "verbose"
    }
}
content = json.dumps(json_request)
header = f"Content-Length: {len(content)}\r\n\r\n"
message = header + content

print(message)