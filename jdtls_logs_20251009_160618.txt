JDTLS日志 - 2025-10-09 16:06:18
==================================================
[stderr] OpenJDK 64-Bit Server VM warning: Options -Xverify:none and -noverify were deprecated in JDK 13 and will likely be removed in a future release.
[stderr] Oct 09, 2025 3:54:19 PM org.apache.aries.spifly.BaseActivator log
[stderr] INFO: Registered provider ch.qos.logback.classic.spi.LogbackServiceProvider of service org.slf4j.spi.SLF4JServiceProvider in bundle ch.qos.logback.classic
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"window/logMessage","params":{"type":3,"message":"Oct 9, 2025, 3:54:23 PM class org.eclipse.jdt.ls.core.internal.JavaLanguageServerPlugin is started"}}Content-Length: 145
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"window/logMessage","params":{"type":3,"message":"Oct 9, 2025, 3:54:23 PM Started org.eclipse.buildship.core 287ms"}}Content-Length: 137
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"window/logMessage","params":{"type":3,"message":"Oct 9, 2025, 3:54:23 PM Started org.eclipse.m2e.core 0ms"}}Content-Length: 127
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"window/logMessage","params":{"type":3,"message":"Oct 9, 2025, 3:54:23 PM Main thread is waiting"}}Content-Length: 128
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"window/logMessage","params":{"type":3,"message":"Oct 9, 2025, 3:54:23 PM \u003e\u003e initialize"}}Content-Length: 158
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"window/logMessage","params":{"type":3,"message":"Oct 9, 2025, 3:54:23 PM Initializing Java Language Server 1.51.0.202510022025"}}Content-Length: 146
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"window/logMessage","params":{"type":3,"message":"Oct 9, 2025, 3:54:25 PM ProjectRegistryRefreshJob finished 3088ms"}}Content-Length: 124
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"window/logMessage","params":{"type":3,"message":"Oct 9, 2025, 3:54:26 PM Static Commands: []"}}Content-Length: 1069
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"window/logMessage","params":{"type":3,"message":"Oct 9, 2025, 3:54:26 PM Non-Static Commands: [java.project.import, java.project.changeImportedProjects, java.navigate.openTypeHierarchy, java.project.resolveStackTraceLocation, java.edit.handlePasteEvent, java.edit.stringFormatting, java.project.getSettings, java.project.resolveWorkspaceSymbol, java.project.upgradeGradle, java.project.createModuleInfo, java.vm.getAllInstalls, java.edit.organizeImports, java.project.refreshDiagnostics, java.project.removeFromSourcePath, java.project.listSourcePaths, java.project.updateSettings, java.project.getAll, java.reloadBundles, java.project.isTestFile, java.project.resolveText, java.project.getClasspaths, java.navigate.resolveTypeHierarchy, java.getTroubleshootingInfo, java.edit.smartSemicolonDetection, java.project.updateSourceAttachment, java.project.updateClassPaths, java.decompile, java.protobuf.generateSources, java.project.resolveSourceAttachment, java.project.updateJdk, java.project.addToSourcePath, java.completion.onDidSelect]"}}Content-Length: 93
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"language/status","params":{"type":"Starting","message":"Init..."}}Content-Length: 118
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"language/status","params":{"type":"Starting","message":"0% Starting Java Language Server"}}Content-Length: 2743
[stdout] 
[stdout] {"jsonrpc":"2.0","id":1,"result":{"capabilities":{"textDocumentSync":{"openClose":true,"change":2,"save":{"includeText":true}},"hoverProvider":true,"completionProvider":{"resolveProvider":true,"triggerCharacters":[".","@","#","*"," "]},"signatureHelpProvider":{"triggerCharacters":["(",","]},"definitionProvider":true,"typeDefinitionProvider":true,"implementationProvider":true,"referencesProvider":true,"documentHighlightProvider":true,"documentSymbolProvider":true,"workspaceSymbolProvider":true,"codeActionProvider":{"codeActionKinds":[],"resolveProvider":false},"codeLensProvider":{"resolveProvider":true},"documentFormattingProvider":true,"documentRangeFormattingProvider":true,"documentOnTypeFormattingProvider":{"firstTriggerCharacter":";","moreTriggerCharacter":["\n","}"]},"renameProvider":{"prepareProvider":true},"foldingRangeProvider":true,"declarationProvider":true,"executeCommandProvider":{"commands":["java.project.import","java.project.changeImportedProjects","java.navigate.openTypeHierarchy","java.project.resolveStackTraceLocation","java.edit.handlePasteEvent","java.edit.stringFormatting","java.project.getSettings","java.project.resolveWorkspaceSymbol","java.project.upgradeGradle","java.project.createModuleInfo","java.vm.getAllInstalls","java.edit.organizeImports","java.project.refreshDiagnostics","java.project.removeFromSourcePath","java.project.listSourcePaths","java.project.updateSettings","java.project.getAll","java.reloadBundles","java.project.isTestFile","java.project.resolveText","java.project.getClasspaths","java.navigate.resolveTypeHierarchy","java.getTroubleshootingInfo","java.edit.smartSemicolonDetection","java.project.updateSourceAttachment","java.project.updateClassPaths","java.decompile","java.protobuf.generateSources","java.project.resolveSourceAttachment","java.project.updateJdk","java.project.addToSourcePath","java.completion.onDidSelect"]},"workspace":{"workspaceFolders":{"supported":true,"changeNotifications":true}},"typeHierarchyProvider":true,"callHierarchyProvider":true,"selectionRangeProvider":true,"semanticTokensProvider":{"legend":{"tokenTypes":["namespace","class","interface","enum","enumMember","type","typeParameter","method","property","variable","parameter","modifier","keyword","annotation","annotationMember","record","recordComponent"],"tokenModifiers":["abstract","static","readonly","deprecated","declaration","documentation","public","private","protected","native","generic","typeArgument","importDeclaration","constructor"]},"range":false,"full":{"delta":false},"documentSelector":[{"language":"java","scheme":"file"},{"language":"java","scheme":"jdt"}]},"inlayHintProvider":true},"serverInfo":{"name":"JDT Language Server (Standard)","version":"1.51.0-SNAPSHOT"}}}Content-Length: 129
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"window/logMessage","params":{"type":3,"message":"Oct 9, 2025, 3:54:26 PM \u003e\u003e initialized"}}Content-Length: 145
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"window/logMessage","params":{"type":3,"message":"Oct 9, 2025, 3:54:26 PM RepositoryRegistryUpdateJob finished 1ms"}}Content-Length: 119
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"language/status","params":{"type":"Starting","message":"21% Starting Java Language Server"}}Content-Length: 131
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"window/logMessage","params":{"type":3,"message":"Oct 9, 2025, 3:54:26 PM Importing Maven project(s)"}}Content-Length: 98
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"language/status","params":{"type":"ProjectStatus","message":"WARNING"}}Content-Length: 164
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"language/status","params":{"type":"Starting","message":"100% Starting Java Language Server - Opening \u0027daip-patcher-swagger\u0027."}}Content-Length: 135
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"window/logMessage","params":{"type":3,"message":"Oct 9, 2025, 3:54:26 PM Workspace initialized in 333ms"}}Content-Length: 90
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"language/status","params":{"type":"Started","message":"Ready"}}Content-Length: 164
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"language/status","params":{"type":"Starting","message":"100% Starting Java Language Server - Opening \u0027daip-patcher-swagger\u0027."}}Content-Length: 145
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"window/logMessage","params":{"type":3,"message":"Oct 9, 2025, 3:54:26 PM \u003e\u003e initialization job finished"}}Content-Length: 185
[stdout] 
[stdout] {"jsonrpc":"2.0","id":"1","method":"client/registerCapability","params":{"registrations":[{"id":"8247af22-2620-4ed5-abba-ee1949b05ff7","method":"workspace/didChangeWorkspaceFolders"}]}}Content-Length: 102
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"language/status","params":{"type":"ServiceReady","message":"ServiceReady"}}Content-Length: 137
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"window/logMessage","params":{"type":3,"message":"Oct 9, 2025, 3:54:26 PM \u003e\u003e build jobs finished"}}Content-Length: 140
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"window/logMessage","params":{"type":3,"message":"Oct 9, 2025, 3:54:26 PM \u003e\u003e registerWatchers\u0027"}}Content-Length: 707
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"textDocument/publishDiagnostics","params":{"uri":"file:///home/<USER>/Codebase/gerrit/PATCHER/daip-patcher-service/daip-patcher-infrastructure-api/pom.xml","diagnostics":[{"range":{"start":{"line":0,"character":1},"end":{"line":0,"character":1}},"severity":1,"code":"0","source":"Java","message":"Project build error: Non-resolvable parent POM for com.zte.daip.manager.patcher:daip-patcher:deletePatch: The following artifacts could not be resolved: com.zte.daip.manager:daip-dependencies:pom:15.5.1-SNAPSHOT (absent): Could not find artifact com.zte.daip.manager:daip-dependencies:pom:15.5.1-SNAPSHOT and \u0027parent.relativePath\u0027 points at wrong local POM"}]}}Content-Length: 732
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"textDocument/publishDiagnostics","params":{"uri":"file:///home/<USER>/Codebase/gerrit/PATCHER/daip-patcher-handler/daip-patcher-handler-impl/daip-patcher-handler-impl-paas/pom.xml","diagnostics":[{"range":{"start":{"line":0,"character":1},"end":{"line":0,"character":1}},"severity":1,"code":"0","source":"Java","message":"Project build error: Non-resolvable parent POM for com.zte.daip.manager.patcher:daip-patcher:deletePatch: The following artifacts could not be resolved: com.zte.daip.manager:daip-dependencies:pom:15.5.1-SNAPSHOT (absent): Could not find artifact com.zte.daip.manager:daip-dependencies:pom:15.5.1-SNAPSHOT and \u0027parent.relativePath\u0027 points at wrong local POM"}]}}Content-Length: 701
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"textDocument/publishDiagnostics","params":{"uri":"file:///home/<USER>/Codebase/gerrit/PATCHER/daip-patcher-handler/daip-patcher-handler-impl/pom.xml","diagnostics":[{"range":{"start":{"line":0,"character":1},"end":{"line":0,"character":1}},"severity":1,"code":"0","source":"Java","message":"Project build error: Non-resolvable parent POM for com.zte.daip.manager.patcher:daip-patcher:deletePatch: The following artifacts could not be resolved: com.zte.daip.manager:daip-dependencies:pom:15.5.1-SNAPSHOT (absent): Could not find artifact com.zte.daip.manager:daip-dependencies:pom:15.5.1-SNAPSHOT and \u0027parent.relativePath\u0027 points at wrong local POM"}]}}Content-Length: 716
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"textDocument/publishDiagnostics","params":{"uri":"file:///home/<USER>/Codebase/gerrit/PATCHER/daip-patcher-service/daip-patcher-impl/daip-patcher-impl-test/pom.xml","diagnostics":[{"range":{"start":{"line":0,"character":1},"end":{"line":0,"character":1}},"severity":1,"code":"0","source":"Java","message":"Project build error: Non-resolvable parent POM for com.zte.daip.manager.patcher:daip-patcher:deletePatch: The following artifacts could not be resolved: com.zte.daip.manager:daip-dependencies:pom:15.5.1-SNAPSHOT (absent): Could not find artifact com.zte.daip.manager:daip-dependencies:pom:15.5.1-SNAPSHOT and \u0027parent.relativePath\u0027 points at wrong local POM"}]}}Content-Length: 693
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"textDocument/publishDiagnostics","params":{"uri":"file:///home/<USER>/Codebase/gerrit/PATCHER/daip-patcher-service/daip-patcher-impl/pom.xml","diagnostics":[{"range":{"start":{"line":0,"character":1},"end":{"line":0,"character":1}},"severity":1,"code":"0","source":"Java","message":"Project build error: Non-resolvable parent POM for com.zte.daip.manager.patcher:daip-patcher:deletePatch: The following artifacts could not be resolved: com.zte.daip.manager:daip-dependencies:pom:15.5.1-SNAPSHOT (absent): Could not find artifact com.zte.daip.manager:daip-dependencies:pom:15.5.1-SNAPSHOT and \u0027parent.relativePath\u0027 points at wrong local POM"}]}}Content-Length: 716
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"textDocument/publishDiagnostics","params":{"uri":"file:///home/<USER>/Codebase/gerrit/PATCHER/daip-patcher-service/daip-patcher-impl/daip-patcher-impl-paas/pom.xml","diagnostics":[{"range":{"start":{"line":0,"character":1},"end":{"line":0,"character":1}},"severity":1,"code":"0","source":"Java","message":"Project build error: Non-resolvable parent POM for com.zte.daip.manager.patcher:daip-patcher:deletePatch: The following artifacts could not be resolved: com.zte.daip.manager:daip-dependencies:pom:15.5.1-SNAPSHOT (absent): Could not find artifact com.zte.daip.manager:daip-dependencies:pom:15.5.1-SNAPSHOT and \u0027parent.relativePath\u0027 points at wrong local POM"}]}}Content-Length: 700
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"textDocument/publishDiagnostics","params":{"uri":"file:///home/<USER>/Codebase/gerrit/PATCHER/daip-patcher-handler/daip-patcher-handler-api/pom.xml","diagnostics":[{"range":{"start":{"line":0,"character":1},"end":{"line":0,"character":1}},"severity":1,"code":"0","source":"Java","message":"Project build error: Non-resolvable parent POM for com.zte.daip.manager.patcher:daip-patcher:deletePatch: The following artifacts could not be resolved: com.zte.daip.manager:daip-dependencies:pom:15.5.1-SNAPSHOT (absent): Could not find artifact com.zte.daip.manager:daip-dependencies:pom:15.5.1-SNAPSHOT and \u0027parent.relativePath\u0027 points at wrong local POM"}]}}Content-Length: 675
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"textDocument/publishDiagnostics","params":{"uri":"file:///home/<USER>/Codebase/gerrit/PATCHER/daip-patcher-handler/pom.xml","diagnostics":[{"range":{"start":{"line":0,"character":1},"end":{"line":0,"character":1}},"severity":1,"code":"0","source":"Java","message":"Project build error: Non-resolvable parent POM for com.zte.daip.manager.patcher:daip-patcher:deletePatch: The following artifacts could not be resolved: com.zte.daip.manager:daip-dependencies:pom:15.5.1-SNAPSHOT (absent): Could not find artifact com.zte.daip.manager:daip-dependencies:pom:15.5.1-SNAPSHOT and \u0027parent.relativePath\u0027 points at wrong local POM"}]}}Content-Length: 695
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"textDocument/publishDiagnostics","params":{"uri":"file:///home/<USER>/Codebase/gerrit/PATCHER/daip-patcher-service/daip-patcher-domain/pom.xml","diagnostics":[{"range":{"start":{"line":0,"character":1},"end":{"line":0,"character":1}},"severity":1,"code":"0","source":"Java","message":"Project build error: Non-resolvable parent POM for com.zte.daip.manager.patcher:daip-patcher:deletePatch: The following artifacts could not be resolved: com.zte.daip.manager:daip-dependencies:pom:15.5.1-SNAPSHOT (absent): Could not find artifact com.zte.daip.manager:daip-dependencies:pom:15.5.1-SNAPSHOT and \u0027parent.relativePath\u0027 points at wrong local POM"}]}}Content-Length: 699
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"textDocument/publishDiagnostics","params":{"uri":"file:///home/<USER>/Codebase/gerrit/PATCHER/daip-patcher-service/daip-patcher-interfaces/pom.xml","diagnostics":[{"range":{"start":{"line":0,"character":1},"end":{"line":0,"character":1}},"severity":1,"code":"0","source":"Java","message":"Project build error: Non-resolvable parent POM for com.zte.daip.manager.patcher:daip-patcher:deletePatch: The following artifacts could not be resolved: com.zte.daip.manager:daip-dependencies:pom:15.5.1-SNAPSHOT (absent): Could not find artifact com.zte.daip.manager:daip-dependencies:pom:15.5.1-SNAPSHOT and \u0027parent.relativePath\u0027 points at wrong local POM"}]}}Content-Length: 671
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"textDocument/publishDiagnostics","params":{"uri":"file:///home/<USER>/Codebase/gerrit/PATCHER/daip-patcher-iui/pom.xml","diagnostics":[{"range":{"start":{"line":0,"character":1},"end":{"line":0,"character":1}},"severity":1,"code":"0","source":"Java","message":"Project build error: Non-resolvable parent POM for com.zte.daip.manager.patcher:daip-patcher:deletePatch: The following artifacts could not be resolved: com.zte.daip.manager:daip-dependencies:pom:15.5.1-SNAPSHOT (absent): Could not find artifact com.zte.daip.manager:daip-dependencies:pom:15.5.1-SNAPSHOT and \u0027parent.relativePath\u0027 points at wrong local POM"}]}}Content-Length: 729
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"textDocument/publishDiagnostics","params":{"uri":"file:///home/<USER>/Codebase/gerrit/PATCHER/daip-patcher-service/daip-patcher-impl/daip-patcher-impl-inner-client-paas/pom.xml","diagnostics":[{"range":{"start":{"line":0,"character":1},"end":{"line":0,"character":1}},"severity":1,"code":"0","source":"Java","message":"Project build error: Non-resolvable parent POM for com.zte.daip.manager.patcher:daip-patcher:deletePatch: The following artifacts could not be resolved: com.zte.daip.manager:daip-dependencies:pom:15.5.1-SNAPSHOT (absent): Could not find artifact com.zte.daip.manager:daip-dependencies:pom:15.5.1-SNAPSHOT and \u0027parent.relativePath\u0027 points at wrong local POM"}]}}Content-Length: 700
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"textDocument/publishDiagnostics","params":{"uri":"file:///home/<USER>/Codebase/gerrit/PATCHER/daip-patcher-service/daip-patcher-application/pom.xml","diagnostics":[{"range":{"start":{"line":0,"character":1},"end":{"line":0,"character":1}},"severity":1,"code":"0","source":"Java","message":"Project build error: Non-resolvable parent POM for com.zte.daip.manager.patcher:daip-patcher:deletePatch: The following artifacts could not be resolved: com.zte.daip.manager:daip-dependencies:pom:15.5.1-SNAPSHOT (absent): Could not find artifact com.zte.daip.manager:daip-dependencies:pom:15.5.1-SNAPSHOT and \u0027parent.relativePath\u0027 points at wrong local POM"}]}}Content-Length: 672
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"textDocument/publishDiagnostics","params":{"uri":"file:///home/<USER>/Codebase/gerrit/PATCHER/daip-patcher-init/pom.xml","diagnostics":[{"range":{"start":{"line":0,"character":1},"end":{"line":0,"character":1}},"severity":1,"code":"0","source":"Java","message":"Project build error: Non-resolvable parent POM for com.zte.daip.manager.patcher:daip-patcher:deletePatch: The following artifacts could not be resolved: com.zte.daip.manager:daip-dependencies:pom:15.5.1-SNAPSHOT (absent): Could not find artifact com.zte.daip.manager:daip-dependencies:pom:15.5.1-SNAPSHOT and \u0027parent.relativePath\u0027 points at wrong local POM"}]}}Content-Length: 675
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"textDocument/publishDiagnostics","params":{"uri":"file:///home/<USER>/Codebase/gerrit/PATCHER/daip-patcher-service/pom.xml","diagnostics":[{"range":{"start":{"line":0,"character":1},"end":{"line":0,"character":1}},"severity":1,"code":"0","source":"Java","message":"Project build error: Non-resolvable parent POM for com.zte.daip.manager.patcher:daip-patcher:deletePatch: The following artifacts could not be resolved: com.zte.daip.manager:daip-dependencies:pom:15.5.1-SNAPSHOT (absent): Could not find artifact com.zte.daip.manager:daip-dependencies:pom:15.5.1-SNAPSHOT and \u0027parent.relativePath\u0027 points at wrong local POM"}]}}Content-Length: 679
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"textDocument/publishDiagnostics","params":{"uri":"file:///home/<USER>/Codebase/gerrit/PATCHER/daip-patcher-task-worker/pom.xml","diagnostics":[{"range":{"start":{"line":0,"character":1},"end":{"line":0,"character":1}},"severity":1,"code":"0","source":"Java","message":"Project build error: Non-resolvable parent POM for com.zte.daip.manager.patcher:daip-patcher:deletePatch: The following artifacts could not be resolved: com.zte.daip.manager:daip-dependencies:pom:15.5.1-SNAPSHOT (absent): Could not find artifact com.zte.daip.manager:daip-dependencies:pom:15.5.1-SNAPSHOT and \u0027parent.relativePath\u0027 points at wrong local POM"}]}}Content-Length: 675
[stdout] 
[stdout] {"jsonrpc":"2.0","method":"textDocument/publishDiagnostics","params":{"uri":"file:///home/<USER>/Codebase/gerrit/PATCHER/daip-patcher-swagger/pom.xml","diagnostics":[{"range":{"start":{"line":0,"character":1},"end":{"line":0,"character":1}},"severity":1,"code":"0","source":"Java","message":"Project build error: Non-resolvable parent POM for com.zte.daip.manager.patcher:daip-patcher:deletePatch: The following artifacts could not be resolved: com.zte.daip.manager:daip-dependencies:pom:15.5.1-SNAPSHOT (absent): Could not find artifact com.zte.daip.manager:daip-dependencies:pom:15.5.1-SNAPSHOT and \u0027parent.relativePath\u0027 points at wrong local POM"}]}}Content-Length: 698
[stdout] 
