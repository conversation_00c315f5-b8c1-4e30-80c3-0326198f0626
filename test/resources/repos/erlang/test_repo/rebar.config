%% Rebar3 configuration for test repository
{erl_opts, [
    debug_info,
    warnings_as_errors,
    warn_export_all,
    warn_unused_import,
    {i, "include"}
]}.

{deps, [
    {eunit, ".*", {git, "https://github.com/richcarl/eunit.git", {tag, "2.3.6"}}}
]}.

{profiles, [
    {test, [
        {erl_opts, [debug_info]},
        {deps, [
            {proper, "1.3.0"}
        ]}
    ]}
]}.

{cover_enabled, true}.
{cover_print_enabled, true}.

{dialyzer, [
    {warnings, [
        unmatched_returns,
        error_handling,
        race_conditions,
        underspecs
    ]}
]}.