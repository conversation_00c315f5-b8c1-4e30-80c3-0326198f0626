<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<libraryInfos>
    <libraryInfo home="/usr/lib/jvm/java-1.8.0-openjdk-1.8.0.312.b07-1.el8.x86_64/jre" version="1.8.0_312">
        <bootpath>
            <entry path="/usr/lib/jvm/java-1.8.0-openjdk-1.8.0.312.b07-1.el8.x86_64/jre/lib/resources.jar"/>
            <entry path="/usr/lib/jvm/java-1.8.0-openjdk-1.8.0.312.b07-1.el8.x86_64/jre/lib/rt.jar"/>
            <entry path="/usr/lib/jvm/java-1.8.0-openjdk-1.8.0.312.b07-1.el8.x86_64/jre/lib/sunrsasign.jar"/>
            <entry path="/usr/lib/jvm/java-1.8.0-openjdk-1.8.0.312.b07-1.el8.x86_64/jre/lib/jsse.jar"/>
            <entry path="/usr/lib/jvm/java-1.8.0-openjdk-1.8.0.312.b07-1.el8.x86_64/jre/lib/jce.jar"/>
            <entry path="/usr/lib/jvm/java-1.8.0-openjdk-1.8.0.312.b07-1.el8.x86_64/jre/lib/charsets.jar"/>
            <entry path="/usr/lib/jvm/java-1.8.0-openjdk-1.8.0.312.b07-1.el8.x86_64/jre/lib/jfr.jar"/>
            <entry path="/usr/lib/jvm/java-1.8.0-openjdk-1.8.0.312.b07-1.el8.x86_64/jre/classes"/>
        </bootpath>
        <extensionDirs>
            <entry path="/usr/lib/jvm/java-1.8.0-openjdk-1.8.0.312.b07-1.el8.x86_64/jre/lib/ext"/>
            <entry path="/usr/java/packages/lib/ext"/>
        </extensionDirs>
        <endorsedDirs>
            <entry path="/usr/lib/jvm/java-1.8.0-openjdk-1.8.0.312.b07-1.el8.x86_64/jre/lib/endorsed"/>
        </endorsedDirs>
    </libraryInfo>
    <libraryInfo home="/home/<USER>/.serena/language_servers/static/EclipseJDTLS/vscode-java/extension/jre/21.0.7-linux-x86_64" version="21.0.7"/>
    <libraryInfo home="/usr/lib/jvm/java-14-openjdk-*********-3.rolling.el8.x86_64-slowdebug" version="14"/>
    <libraryInfo home="/usr/lib/jvm/java-1.8.0-openjdk-1.8.0.312.b07-1.el8.x86_64" version="1.8.0_312">
        <bootpath>
            <entry path="/usr/lib/jvm/java-1.8.0-openjdk-1.8.0.312.b07-1.el8.x86_64/jre/lib/resources.jar"/>
            <entry path="/usr/lib/jvm/java-1.8.0-openjdk-1.8.0.312.b07-1.el8.x86_64/jre/lib/rt.jar"/>
            <entry path="/usr/lib/jvm/java-1.8.0-openjdk-1.8.0.312.b07-1.el8.x86_64/jre/lib/sunrsasign.jar"/>
            <entry path="/usr/lib/jvm/java-1.8.0-openjdk-1.8.0.312.b07-1.el8.x86_64/jre/lib/jsse.jar"/>
            <entry path="/usr/lib/jvm/java-1.8.0-openjdk-1.8.0.312.b07-1.el8.x86_64/jre/lib/jce.jar"/>
            <entry path="/usr/lib/jvm/java-1.8.0-openjdk-1.8.0.312.b07-1.el8.x86_64/jre/lib/charsets.jar"/>
            <entry path="/usr/lib/jvm/java-1.8.0-openjdk-1.8.0.312.b07-1.el8.x86_64/jre/lib/jfr.jar"/>
            <entry path="/usr/lib/jvm/java-1.8.0-openjdk-1.8.0.312.b07-1.el8.x86_64/jre/classes"/>
        </bootpath>
        <extensionDirs>
            <entry path="/usr/lib/jvm/java-1.8.0-openjdk-1.8.0.312.b07-1.el8.x86_64/jre/lib/ext"/>
            <entry path="/usr/java/packages/lib/ext"/>
        </extensionDirs>
        <endorsedDirs>
            <entry path="/usr/lib/jvm/java-1.8.0-openjdk-1.8.0.312.b07-1.el8.x86_64/jre/lib/endorsed"/>
        </endorsedDirs>
    </libraryInfo>
    <libraryInfo home="/usr/lib/jvm/java-14-openjdk-*********-3.rolling.el8.x86_64" version="14"/>
</libraryInfos>
