eclipse.preferences.version=1
org.eclipse.jdt.core.builder.resourceCopyExclusionFilter=
org.eclipse.jdt.core.circularClasspath=warning
org.eclipse.jdt.core.codeComplete.subwordMatch=disabled
org.eclipse.jdt.core.codeComplete.visibilityCheck=enabled
org.eclipse.jdt.core.compiler.codegen.targetPlatform=21
org.eclipse.jdt.core.compiler.compliance=21
org.eclipse.jdt.core.compiler.ignoreUnnamedModuleForSplitPackage=enabled
org.eclipse.jdt.core.compiler.problem.missingSerialVersion=ignore
org.eclipse.jdt.core.compiler.problem.redundantSuperinterface=warning
org.eclipse.jdt.core.compiler.problem.unhandledWarningToken=ignore
org.eclipse.jdt.core.compiler.release=enabled
org.eclipse.jdt.core.compiler.source=21
org.eclipse.jdt.core.formatter.indent_switchstatements_compare_to_switch=true
org.eclipse.jdt.core.formatter.join_lines_in_comments=false
org.eclipse.jdt.core.formatter.join_wrapped_lines=false
